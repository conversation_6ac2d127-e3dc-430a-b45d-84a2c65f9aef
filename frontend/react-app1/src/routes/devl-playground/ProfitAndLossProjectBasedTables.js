import React from 'react';
import { CheckOutlined, SyncOutlined } from '@ant-design/icons';
import { Alert, Button, Col, Row, Skeleton, Table, Tag, Tooltip } from 'antd';
import { useState } from 'react';
import SkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader';
import { getRandomTagBgColor, isMobileView } from '../../util/helpers';

function getDummyAuthorities() {
    return [
        { key: 'user1', label: 'Administrator' },
        { key: 'user2', label: 'Manager' },
        { key: 'user3', label: 'Supervisor' },
        { key: 'user4', label: 'Supervisor' },
    ];
}

const form_data = {
    user1: 'user_1',
    user2: 'user_2',
    user3: 'user_3',
    user4: 'user_4',
};

function UserName({ id, customError }) {
    const userNames = {
        user_1: 'Jain<PERSON>alia',
        user_2: '<PERSON><PERSON><PERSON> <PERSON>',
        user_3: '<PERSON><PERSON><PERSON>',
        user_4: '<PERSON><PERSON><PERSON>',
    };
    return userNames[id] || customError;
}

const ProfiData = [
    { label: 'GM%', value: '25%', className: 'gx-text-green' },
    { label: 'Net GM', value: '₹300.00', className: 'gx-text-green' },
    {
        label: 'Total Revenue',
        value: '₹1,200.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
    {
        label: 'Total Cost',
        value: '₹900.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
];

const LossData = [
    { label: 'GM%', value: '-10%', className: 'gx-text-red' },
    { label: 'Net GM', value: '-₹120.00', className: 'gx-text-red' },
    {
        label: 'Total Revenue',
        value: '₹1,200.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
    {
        label: 'Total Cost',
        value: '₹1320.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
];

const revenueBreakdownCols = [
    {
        title: 'ITEM NAME',
        dataIndex: 'itemName',
        key: 'itemName',
    },
    {
        title: 'QUANTITY',
        dataIndex: 'quantity',
        key: 'quantity',
    },
    {
        title: 'EXPECTED REVENUE',
        dataIndex: 'expectedRevenue',
        key: 'expectedRevenue',
    },
    {
        title: 'ACTUAL REVENUE',
        dataIndex: 'actualRevenue',
        key: 'actualRevenue',
    },
];

const revenueTableData = [
    {
        key: '1',
        itemName: 'Curtain - Double Track Rail',
        quantity: 2,
        expectedRevenue: '₹1,200.00',
        actualRevenue: '₹1,200.00',
    },
];
const revenueFixedBreakdownCols = [
    {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        fixed: 'left',
        width: 100,
    },
    {
        title: 'Item',
        dataIndex: 'item',
        key: 'item',
    },
    {
        title: 'Unit',
        dataIndex: 'unit',
        key: 'unit',
    },
    {
        title: 'Expected Qty',
        dataIndex: 'expectedQty',
        key: 'expectedQty',
    },
    {
        title: 'Actual Qty',
        dataIndex: 'actualQty',
        key: 'actualQty',
    },
    {
        title: 'Expected Revenue',
        dataIndex: 'expectedRevenue',
        key: 'expectedRevenue',
    },
    {
        title: 'Actual Revenue',
        dataIndex: 'actualRevenue',
        key: 'actualRevenue',
        fixed: 'right',
        width: 100,
    },
];

const revenueFixedTableData = [
    {
        key: '1',
        type: 'Kitchen',
        item: 'Base Cabinet - A',
        unit: 'SQFT',
        expectedQty: 14.12,
        actualQty: 14.12,
        expectedRevenue: '₹1,200.00',
        actualRevenue: '₹1,200.00',
    },
    {
        key: '2',
        type: 'Kitchen',
        item: 'Base Cabinet - B',
        unit: 'SQFT',
        expectedQty: 14.99,
        actualQty: 14.99,
        expectedRevenue: '₹1,123.00',
        actualRevenue: '₹1,123.00',
    },
    {
        key: '3',
        type: 'Kitchen',
        item: 'Base Cabinet - B',
        unit: 'SQFT',
        expectedQty: 14.99,
        actualQty: 14.99,
        expectedRevenue: '₹1,123.00',
        actualRevenue: '₹1,123.00',
    },
    {
        key: '4',
        type: 'Kitchen',
        item: 'Base Cabinet - B',
        unit: 'SQFT',
        expectedQty: 14.99,
        actualQty: 14.99,
        expectedRevenue: '₹1,123.00',
        actualRevenue: '₹1,123.00',
    },
    {
        key: '5',
        type: 'Kitchen',
        item: 'Base Cabinet - B',
        unit: 'SQFT',
        expectedQty: 14.99,
        actualQty: 14.99,
        expectedRevenue: '₹1,123.00',
        actualRevenue: '₹1,123.00',
    },
    {
        key: '6',
        type: 'Kitchen',
        item: 'Base Cabinet - B',
        unit: 'SQFT',
        expectedQty: 14.99,
        actualQty: 14.99,
        expectedRevenue: '₹1,123.00',
        actualRevenue: '₹1,123.00',
    },
];

const inHouseCostbreakdownCols = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: ' Mandays',
        dataIndex: 'mandays',
        key: 'mandays',
    },
    {
        title: 'Manday Cost',
        dataIndex: 'mandayCost',
        key: 'mandayCost',
    },
    {
        title: 'EXPECTED COST',
        dataIndex: 'expectedCost',
        key: 'expectedCost',
    },
    {
        title: 'ACTUAL COST',
        dataIndex: 'actualCost',
        key: 'actualCost',
        fixed: 'right',
        width: 100,
    },
];

const inHouseCostbreakdownData = [
    {
        key: '1',
        name: 'Tahabur Khan',
        mandays: '1',
        mandayCost: '₹100.00',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Barun Adak',
        mandays: '4',
        mandayCost: '₹100.00',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
];

const vendorCostbreakdownCols = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: ' TYPE',
        dataIndex: 'resourceType',
        key: 'resourceType',
    },
    {
        title: 'Sqft',
        dataIndex: 'technicianCost',
        key: 'technicianCost',
    },
    {
        title: 'EXPECTED COST',
        dataIndex: 'expectedCost',
        key: 'expectedCost',
    },
    {
        title: 'ACTUAL COST',
        dataIndex: 'actualCost',
        key: 'actualCost',
        fixed: 'right',
        width: 100,
    },
];

const vendorCostbreakdownData = [
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Advance',
        technicianCost: '-',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Full',
        technicianCost: '17',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Full',
        technicianCost: '17',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Full',
        technicianCost: '17',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Full',
        technicianCost: '17',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Full',
        technicianCost: '17',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
    {
        key: '1',
        name: 'Rudra kumar (CONKOA001539)',
        resourceType: 'Full',
        technicianCost: '17',
        expectedCost: '₹100.00',
        actualCost: '₹100.00',
    },
];

const inHouseCostTableFooterData = [
    {
        label: 'Total Inhouse Cost',
        values: '₹1,000.00',
        className:
            'wy-mkw-table-tota-bg wy-mkw-table-blue-bg wy-mkw-table-footer-data',
    },
];
const vendorCostTableFooterData = [
    {
        label: 'Total Vendor Cost',
        values: '₹1,000.00',
        className:
            'wy-mkw-table-tota-bg wy-mkw-table-blue-bg wy-mkw-table-footer-data',
    },
];

const mkwTablerows = [
    {
        label: 'Subtotal',
        values: '₹9959.20',
        className: 'wy-mkw-table-footer-data',
    },
    {
        label: 'Discount',
        values: '₹500.00',
        className: 'wy-mkw-table-red wy-mkw-table-footer-data',
    },
    {
        label: 'Total Revenue',
        values: '₹9459.20',
        className:
            'wy-mkw-table-tota-bg wy-mkw-table-blue-bg gx-fs-18 wy-mkw-table-footer-data',
    },
];

const ProfitAndLossProjectBasedTables = ({}) => {
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(false);
    const [syncLoading, setSyncLoading] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [showAllNotification, setShowAllNotification] = useState(false);
    const [showProfit, setShowProfit] = useState(true);
    const [noAuthorities, setNoAuthorities] = useState(false);

    const handleShowProfitData = () => {
        setShowProfit(!showProfit);
    };

    const handleToggleAuthoritiesUI = () => {
        setNoAuthorities(!noAuthorities);
    };

    const handleShowAllNotifications = () => {
        setShowAllNotification(!showAllNotification);
    };

    const handleInitialLoading = () => {
        setInitialLoading(true);
        setLoading(true);
        setTimeout(() => {
            setLoading(false);
            setInitialLoading(false);
        }, 2000);
    };

    const handleNotificationSync = () => {
        setSyncLoading(true);
        setIsSyncing(true);
        setLoading(true);

        setTimeout(() => {
            setLoading(false);
            setSyncLoading(false);
            setIsSyncing(false);
        }, 2000);
    };

    const showAllAuthoritiesUI = () => {
        return (
            <div className="scrolling_wrapper gx-mr-3">
                {getDummyAuthorities().map((dummyAuthority) => (
                    <Tooltip
                        title={() => {
                            return (
                                <UserName
                                    id={form_data[dummyAuthority.key]}
                                    customError={'Missing'}
                                />
                            );
                        }}
                        placement="top"
                    >
                        <div className="gx-text-center gx-d-inline-block">
                            <Tag
                                color={`${getRandomTagBgColor()}`}
                                className="wy-tag-line-height-adjust gx-mb-0"
                            >
                                <UserName
                                    id={form_data[dummyAuthority.key]}
                                    customError={'-'}
                                />
                                <br />
                                <small className="gx-mb-0 gx-text-center">
                                    {dummyAuthority.label}
                                </small>
                            </Tag>
                        </div>
                    </Tooltip>
                ))}
            </div>
        );
    };

    const showAllNotificationsUI = () => {
        return (
            <>
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                    <div className="gx-d-flex gx-align-items-center">
                        <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                        <p className="gx-mb-0">Synced successfully</p>
                    </div>
                    <div>
                        <Button
                            success
                            type="primary"
                            className="gx-mb-0 gx-mr-2 wy-sync-pop-btn-success"
                            size={isMobileView() && 'small'}
                        >
                            Ok
                        </Button>
                    </div>
                </div>
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                    <div className="gx-d-flex gx-align-items-center">
                        <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                        <p className="gx-mb-0">Values are up to date.</p>
                    </div>
                    <div>
                        <Button
                            success
                            type="primary"
                            className="gx-mb-0 gx-mr-2 wy-sync-pop-btn-success"
                            size={isMobileView() && 'small'}
                        >
                            Ok
                        </Button>
                    </div>
                </div>
                <div className="gx-d-flex gx-align-items-center gx-justify-content-between wy-sync-popup">
                    <div className="gx-d-flex gx-align-items-center">
                        <SyncOutlined className="gx-text-primary gx-mr-2" />
                        <p className="gx-mb-0">Confirm to update values</p>
                    </div>
                    <div className="gx-d-flex gx-align-items-center">
                        <Button
                            type="link"
                            className="gx-m-0 gx-mr-2 gx-text-danger"
                            size={isMobileView() && 'small'}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            className="gx-m-0"
                            size={isMobileView() && 'small'}
                        >
                            Update
                        </Button>
                    </div>
                </div>
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup">
                    <div className="gx-d-flex gx-align-items-center">
                        <SyncOutlined className="gx-text-primary gx-mr-2" />
                        <p className="gx-mb-0">
                            Values are changed in master, click to change to new
                            values
                        </p>
                    </div>
                    <Button
                        type="primary"
                        icon={<SyncOutlined spin={isSyncing} />}
                        onClick={handleNotificationSync}
                        className="gx-mb-0"
                        size={isMobileView() && 'small'}
                    >
                        Sync
                    </Button>
                </div>
            </>
        );
    };

    const RequestDetails = ({ label, value, loading }) => (
        <div className="wy-request-details-item">
            <div className="wy-request-details-label gx-text-gray gx-fs-sm">
                {loading ? (
                    <div className="animate-pulse wy-loading-description-label-120"></div>
                ) : (
                    label
                )}
            </div>
            <div className="description-value gx-fs-lg gx-text-black">
                {loading ? (
                    <div className="animate-pulse wy-loading-description-label-80"></div>
                ) : (
                    value
                )}
            </div>
        </div>
    );

    return (
        <div className="wy-pnl-wrapper">
            <div className="gx-mt-3 gx-mb-2">
                <p className="gx-fs-18 gx-mb-1">
                    Revenue Breakdown with Fixed Cols for MKW
                </p>
                <div className="wy-extra-table-wrapper">
                    {loading ? (
                        <div className="gx-mb-4">
                            <Skeleton.Input
                                className="wy_skeleton_loading_box"
                                active
                            />
                        </div>
                    ) : (
                        <div className="table-responsive-wy wy-pl-table-style  wy-pl-table-style-border2">
                            <Table
                                columns={revenueFixedBreakdownCols}
                                dataSource={revenueFixedTableData}
                                pagination={{
                                    pageSize: 5,
                                    size: 'small',
                                }}
                                scroll={{
                                    x: 1500,
                                }}
                            />
                        </div>
                    )}
                    {loading ? (
                        <div className="gx-mb-4">
                            <Skeleton.Input
                                className="wy_skeleton_loading_box"
                                active
                            />
                        </div>
                    ) : (
                        <div className=" gx-mt-2 gx-text-right">
                            {mkwTablerows.map((data, index) => (
                                <div key={index} className={data.className}>
                                    <div className="">{data.label}</div>
                                    <div className="">{data.values}</div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
            <div className="gx-mt-3 gx-mb-2 ">
                <p className="gx-fs-18 gx-mb-1 gx-font-weight-bold">
                    Cost Breakdown
                </p>
                <p className="gx-fs-18 gx-mb-1">Inhouse Cost</p>
                <div className="wy-extra-table-wrapper">
                    {loading ? (
                        <div className="gx-mb-4">
                            <Skeleton.Input
                                className="wy_skeleton_loading_box"
                                active
                            />
                        </div>
                    ) : (
                        <div className="table-responsive-wy wy-table-footer-wrapper  wy-pl-table-style-border wy-pl-table-style">
                            <Table
                                columns={inHouseCostbreakdownCols}
                                dataSource={inHouseCostbreakdownData}
                                pagination={false}
                                className="gx-border"
                                scroll={{
                                    x: 1500,
                                }}
                            />
                        </div>
                    )}
                    {loading ? (
                        <div className="gx-mb-4">
                            <Skeleton.Input
                                className="wy_skeleton_loading_box"
                                active
                            />
                        </div>
                    ) : (
                        <div className=" gx-text-right">
                            {inHouseCostTableFooterData.map((data, index) => (
                                <div key={index} className={data.className}>
                                    <div className="">{data.label}</div>
                                    <div className="">{data.values}</div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
                <p className="gx-fs-18 gx-mb-1 gx-mt-3">Vendor Cost</p>
                <div className="wy-extra-table-wrapper">
                    {loading ? (
                        <div className="gx-mb-4">
                            <Skeleton.Input
                                className="wy_skeleton_loading_box"
                                active
                            />
                        </div>
                    ) : (
                        <div className="table-responsive-wy wy-table-footer-wrapper  wy-pl-table-style-border wy-pl-table-style">
                            <Table
                                columns={vendorCostbreakdownCols}
                                dataSource={vendorCostbreakdownData}
                                pagination={false}
                                scroll={{
                                    x: 1500,
                                }}
                            />
                        </div>
                    )}
                    {loading ? (
                        <div className="gx-mb-4">
                            <Skeleton.Input
                                className="wy_skeleton_loading_box"
                                active
                            />
                        </div>
                    ) : (
                        <div className=" gx-text-right">
                            {vendorCostTableFooterData.map((data, index) => (
                                <div key={index} className={data.className}>
                                    <div className="">{data.label}</div>
                                    <div className="">{data.values}</div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
                <div className=" wy-mkw-table-footer-data gx-fs-18 gx-mt-4 gx-text-right wy-pnl-total-cost">
                    <div>Total Cost</div> <div>₹ 2,000.00</div>
                </div>
            </div>
        </div>
    );
};

export default ProfitAndLossProjectBasedTables;
