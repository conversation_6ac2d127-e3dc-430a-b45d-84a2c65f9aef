import { CheckOutlined, SyncOutlined } from '@ant-design/icons';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Col,
    Row,
    Skeleton,
    Switch,
    Table,
    Tag,
    Tooltip,
} from 'antd';
import React, { useState } from 'react';
import SkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader';
import { getRandomTagBgColor, isMobileView } from '../../util/helpers';

function getDummyAuthorities() {
    return [
        { key: 'user1', label: 'Administrator' },
        { key: 'user2', label: 'Manager' },
        { key: 'user3', label: 'Supervisor' },
        { key: 'user4', label: 'Supervisor' },
    ];
}

const form_data = {
    user1: 'user_1',
    user2: 'user_2',
    user3: 'user_3',
    user4: 'user_4',
};

function UserName({ id, customError }) {
    const userNames = {
        user_1: 'Jain<PERSON>',
        user_2: '<PERSON><PERSON><PERSON> <PERSON>',
        user_3: '<PERSON><PERSON><PERSON>',
        user_4: '<PERSON><PERSON><PERSON>',
    };
    return userNames[id] || customError;
}

const ProfiData = [
    { label: 'GM%', value: '25%', className: 'gx-text-green' },
    { label: 'Net GM', value: '₹300.00', className: 'gx-text-green' },
    {
        label: 'Total Revenue',
        value: '₹1,200.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
    {
        label: 'Total Cost',
        value: '₹900.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
];

const LossData = [
    { label: 'GM%', value: '-10%', className: 'gx-text-red' },
    { label: 'Net GM', value: '-₹120.00', className: 'gx-text-red' },
    {
        label: 'Total Revenue',
        value: '₹1,200.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
    {
        label: 'Total Cost',
        value: '₹1320.00',
        className: 'gx-fontweight-bold gx-text-black',
    },
];

const revenueBreakdownCols = [
    {
        title: 'ITEM NAME',
        dataIndex: 'itemName',
        key: 'itemName',
    },
    {
        title: 'QUANTITY',
        dataIndex: 'quantity',
        key: 'quantity',
        width: '250px',
    },
    {
        title: 'EXPECTED REVENUE',
        dataIndex: 'expectedRevenue',
        key: 'expectedRevenue',
        width: '250px',
    },
    {
        title: 'ACTUAL REVENUE',
        dataIndex: 'actualRevenue',
        key: 'actualRevenue',
        width: '250px',
    },
];

const revenueTableData = [
    {
        key: '1',
        itemName: 'Curtain - Double Track Rail',
        quantity: 2,
        expectedRevenue: '₹1,200.00',
        actualRevenue: '₹1,200.00',
    },
    {
        key: '2',
        expectedRevenue: 'Additional Revenue',
        actualRevenue: '₹200.00',
    },

    {
        key: '4',
        expectedRevenue: 'Total Revenue',
        actualRevenue: '₹1,300.00',
    },
];

const costbreakdownCols = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'RESOURCE TYPE',
        dataIndex: 'resourceType',
        key: 'resourceType',
    },
    {
        title: 'TECHNICIAN COST',
        dataIndex: 'technicianCost',
        key: 'technicianCost',
    },
    {
        title: 'MANDAYS',
        dataIndex: 'mandays',
        key: 'mandays',
    },
    {
        title: 'EXPECTED COST',
        dataIndex: 'expectedCost',
        key: 'expectedCost',
        width: '250px',
    },
    {
        title: 'ACTUAL COST',
        dataIndex: 'actualCost',
        key: 'actualCost',
        width: '250px',
    },
];

const costbreakdownData = [
    {
        key: '1',
        name: 'John Smith',
        resourceType: 'Inhouse',
        technicianCost: '₹400.00',
        mandays: 2,
        expectedCost: '₹1,000.00',
        actualCost: '₹800.00',
    },
    {
        key: '2',
        expectedCost: 'Additional Cost',
        actualCost: '₹200.00',
    },
    {
        key: '3',
        expectedCost: 'Deductions',
        actualCost: <span className="gx-text-danger">-₹100.00</span>,
    },
    {
        key: '4',
        expectedCost: 'Total Cost',
        actualCost: '₹900.00',
    },
];

const ProfitAndLossTaskBased = ({}) => {
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(false);
    const [syncLoading, setSyncLoading] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [showAllNotification, setShowAllNotification] = useState(false);
    const [showProfit, setShowProfit] = useState(true);
    const [noAuthorities, setNoAuthorities] = useState(false);

    const handleShowProfitData = () => {
        setShowProfit(!showProfit);
    };

    const handleToggleAuthoritiesUI = () => {
        setNoAuthorities(!noAuthorities);
    };

    const handleShowAllNotifications = () => {
        setShowAllNotification(!showAllNotification);
    };

    const handleInitialLoading = () => {
        setInitialLoading(true);
        setLoading(true);
        setTimeout(() => {
            setLoading(false);
            setInitialLoading(false);
        }, 2000);
    };

    const handleNotificationSync = () => {
        setSyncLoading(true);
        setIsSyncing(true);
        setLoading(true);

        setTimeout(() => {
            setLoading(false);
            setSyncLoading(false);
            setIsSyncing(false);
        }, 2000);
    };

    const showAllAuthoritiesUI = () => {
        return (
            <div className="scrolling_wrapper gx-mr-3">
                {getDummyAuthorities().map((dummyAuthority) => (
                    <Tooltip
                        title={() => {
                            return (
                                <UserName
                                    id={form_data[dummyAuthority.key]}
                                    customError={'Missing'}
                                />
                            );
                        }}
                        placement="top"
                    >
                        <div className="gx-text-center gx-d-inline-block">
                            <Tag
                                color={`${getRandomTagBgColor()}`}
                                className="wy-tag-line-height-adjust gx-mb-0"
                            >
                                <UserName
                                    id={form_data[dummyAuthority.key]}
                                    customError={'-'}
                                />
                                <br />
                                <small className="gx-mb-0 gx-text-center">
                                    {dummyAuthority.label}
                                </small>
                            </Tag>
                        </div>
                    </Tooltip>
                ))}
            </div>
        );
    };

    const showAllNotificationsUI = () => {
        return (
            <>
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                    <div className="gx-d-flex gx-align-items-center">
                        <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                        <p className="gx-mb-0">Synced successfully</p>
                    </div>
                    <div>
                        <Button
                            success
                            type="primary"
                            className="gx-mb-0 gx-mr-2 wy-sync-pop-btn-success"
                            size={isMobileView() && 'small'}
                        >
                            Ok
                        </Button>
                    </div>
                </div>
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                    <div className="gx-d-flex gx-align-items-center">
                        <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                        <p className="gx-mb-0">Values are up to date.</p>
                    </div>
                    <div>
                        <Button
                            success
                            type="primary"
                            className="gx-mb-0 gx-mr-2 wy-sync-pop-btn-success"
                            size={isMobileView() && 'small'}
                        >
                            Ok
                        </Button>
                    </div>
                </div>
                <div className="gx-d-flex gx-align-items-center gx-justify-content-between wy-sync-popup">
                    <div className="gx-d-flex gx-align-items-center">
                        <SyncOutlined className="gx-text-primary gx-mr-2" />
                        <p className="gx-mb-0">Confirm to update values</p>
                    </div>
                    <div className="gx-d-flex gx-align-items-center">
                        <Button
                            type="link"
                            className="gx-m-0 gx-mr-2 gx-text-danger"
                            size={isMobileView() && 'small'}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            className="gx-m-0"
                            size={isMobileView() && 'small'}
                        >
                            Update
                        </Button>
                    </div>
                </div>
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup">
                    <div className="gx-d-flex gx-align-items-center">
                        <SyncOutlined className="gx-text-primary gx-mr-2" />
                        <p className="gx-mb-0">
                            Values are changed in master, click to change to new
                            values
                        </p>
                    </div>
                    <Button
                        type="primary"
                        icon={<SyncOutlined spin={isSyncing} />}
                        onClick={handleNotificationSync}
                        className="gx-mb-0"
                        size={isMobileView() && 'small'}
                    >
                        Sync
                    </Button>
                </div>
            </>
        );
    };

    const RequestDetails = ({ label, value, loading }) => (
        <div className="wy-request-details-item">
            <div className="wy-request-details-label gx-text-gray gx-fs-sm">
                {loading ? (
                    <div className="animate-pulse wy-loading-description-label-120"></div>
                ) : (
                    label
                )}
            </div>
            <div className="description-value gx-fs-lg gx-text-black">
                {loading ? (
                    <div className="animate-pulse wy-loading-description-label-80"></div>
                ) : (
                    value
                )}
            </div>
        </div>
    );

    const [showAll, setShowAll] = useState(true);

    const filteredData = showAll
        ? revenueTableData
        : revenueTableData.filter(
              (item) => item.key !== '2' && item.key !== '4'
          );

    return (
        <div className="wy-pnl-wrapper">
            <div className="gx-mt-3 gx-mb-2">
                <p className="gx-fs-18 gx-mb-1 gx-d-flex gx-justify-content-between">
                    Revenue Breakdown{' '}
                    <Switch
                        checked={showAll}
                        onChange={() => setShowAll((prev) => !prev)}
                        checkedChildren="With Additional Revenue"
                        unCheckedChildren="Without Additional Revenue"
                    />
                </p>
                {loading ? (
                    <div className="gx-mb-4">
                        <Skeleton.Input
                            className="wy_skeleton_loading_box"
                            active
                        />
                    </div>
                ) : (
                    <div className="table-responsive-wy wy-pl-table-style  wy-pl-table-style-border2">
                        <Table
                            columns={revenueBreakdownCols}
                            dataSource={filteredData}
                            pagination={false}
                            className="gx-border"
                        />
                    </div>
                )}
            </div>
            <div className="gx-mt-3 gx-mb-2">
                <p className="gx-fs-18 gx-mb-1">Cost Breakdown</p>
                {loading ? (
                    <div className="gx-mb-4">
                        <Skeleton.Input
                            className="wy_skeleton_loading_box"
                            active
                        />
                    </div>
                ) : (
                    <div className="table-responsive-wy wy-table-footer-wrapper wy-cost-breakdown-table wy-pl-table-style-border wy-pl-table-style">
                        <Table
                            columns={costbreakdownCols}
                            dataSource={costbreakdownData}
                            pagination={false}
                            className="gx-border"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ProfitAndLossTaskBased;
