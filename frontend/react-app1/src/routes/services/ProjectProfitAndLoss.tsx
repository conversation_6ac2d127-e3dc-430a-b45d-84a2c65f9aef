import React, { useEffect } from 'react';
import { Alert, Col, Row, Skeleton, Table, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { FixedType } from 'rc-table/lib/interface';
import { useState } from 'react';
import SkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader';
import {
    convertUTCToDisplayTime,
    getLabelFrmOptionsValue,
    getRandomTagBgColor,
    isMobileView,
} from '../../util/helpers';
import UserNameById from '../../components/wify-utils/UserName';
import http_utils from '../../util/http_utils';
import TableLoading from '../../components/WIFY/WifyComponents/SkeletonLoader/TableLoading';
import moment from 'moment';

interface VendorCostItem {
    key: string;
    name: string;
    value: string;
    resourceType: string;
    technicianCost: string;
    expectedCost: string;
    actualCost: string;
}
interface RevenueTableItem {
    key: string;
    itemName: string;
    quantity: number;
    expectedRevenue: string;
    actualRevenue: string;
}
interface RevenueFixedTableItem {
    key: string;
    type: string;
    item: string;
    unit: string;
    expectedQty: number;
    actualQty: number;
    expectedRevenue: string;
    actualRevenue: string;
}

interface InHouseCostItem {
    key?: string;
    id?: number;
    name: string;
    user_id?: string;
    mandays: string;
    manday_cost: string;
    expected_cost: string;
    actual_cost: string;
}
interface Authority {
    key: string;
    label: string;
}

interface StatusInfo {
    time: string;
    key?: string;
}

interface SrvcReqData {
    latestOpenStatus?: StatusInfo;
    latestClosedStatus?: StatusInfo;
    [key: string]: any;
}

interface SpConfigData {
    vertical_title?: string;
    [key: string]: any;
}

interface ProjectProfitAndLossProps {
    spConfigData?: SpConfigData;
    srvcReqData?: SrvcReqData;
    orgNickName?: string;
    srvcTypeId?: string | number;
    srvcReqId?: string | number;
    roleList?: any[];
    locGrpsName?: string;
    allAuthoritiesOfVertical?: Authority[];
    srvcConfigData?: any;
}

const revenueFixedBreakdownCols: ColumnsType<RevenueFixedTableItem> = [
    {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        fixed: 'left' as FixedType,
        width: 100,
    },
    {
        title: 'Item',
        dataIndex: 'item',
        key: 'item',
        render: (text, record) => <span>{text ? text : 'N/A'}</span>,
    },
    {
        title: 'Unit',
        dataIndex: 'unit',
        key: 'unit',
    },
    {
        title: 'Expected Qty',
        dataIndex: 'qty',
        key: 'qty',
    },
    {
        title: 'Actual Qty',
        dataIndex: 'qty_after_discount',
        key: 'qty_after_discount',
    },
    {
        title: 'Expected Revenue',
        dataIndex: 'sub_total',
        key: 'sub_total',
        render: (text, record) => <span>{text ? `₹${text}` : '₹0.00'}</span>,
    },
    {
        title: 'Actual Revenue',
        dataIndex: 'sub_total_after_discount',
        key: 'sub_total_after_discount',
        fixed: 'right',
        width: 130,
        render: (text, record) => <span>{text ? `₹${text}` : '₹0.00'}</span>,
    },
];

const inHouseCostbreakdownCols: ColumnsType<InHouseCostItem> = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
            <span>
                {record.user_id ? (
                    <UserNameById id={record.user_id} customError="-" />
                ) : (
                    text
                )}
            </span>
        ),
    },
    {
        title: ' Mandays',
        dataIndex: 'mandays',
        key: 'mandays',
    },
    {
        title: 'Manday Cost',
        dataIndex: 'mandays_cost',
        key: 'mandays_cost',
        render: (text, record) => <span>{text ? `₹${text}` : '₹0.00'}</span>,
    },
    {
        title: 'EXPECTED COST',
        dataIndex: 'expected_cost',
        key: 'expected_cost',
        render: (text, record) => <span>{text ? `₹${text}` : '₹0.00'}</span>,
    },
    {
        title: 'ACTUAL COST',
        dataIndex: 'actual_cost',
        key: 'actual_cost',
        fixed: 'right',
        width: 130,
        render: (text, record) => <span>{text ? `₹${text}` : '₹0.00'}</span>,
    },
];

const protoUrl = '/services/project_profit_and_loss';

const ProjectProfitAndLoss: React.FC<ProjectProfitAndLossProps> = (props) => {
    const {
        spConfigData,
        srvcReqData,
        locGrpsName,
        allAuthoritiesOfVertical,
        srvcReqId,
        srvcTypeId,
    } = props;
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | undefined>(undefined);
    const [viewData, setViewData] = useState<any>(undefined);

    const [showProfit] = useState(true);

    useEffect(() => {
        initViewData();
    }, [srvcReqId, srvcTypeId]);

    const initViewData = () => {
        if (viewData == undefined && !loading) {
            setLoading(true);

            let params = {};
            params['vertical_id'] = spConfigData?.entry_id;

            const onComplete = (resp: any) => {
                setViewData(resp.data);
                setLoading(false);
            };

            const onError = (err: any) => {
                setLoading(false);
                setError(http_utils.decodeErrorToMessage(err));
            };

            const url = protoUrl + '/' + srvcTypeId + '/' + srvcReqId;
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    };
    /**
     * Retrieves a filtered list of authority objects.
     *
     * This function filters the global array `allAuthoritiesOfVertical` by checking if a corresponding value exists
     * in the `srvcReqData` object for each authority's key.
     */
    const getAuthorityWithRoles = (): Authority[] | undefined => {
        return allAuthoritiesOfVertical?.filter(
            (singleAuthorityMeta: any) => srvcReqData[singleAuthorityMeta.key]
        );
    };
    interface RequestDetailsUIProps {
        label: string;
        value: string | React.ReactNode;
        loading?: boolean;
    }

    const requestDetailsUI = ({
        label,
        value,
        loading,
    }: RequestDetailsUIProps) => (
        <div className="wy-request-details-item">
            <div className="wy-request-details-label gx-text-gray gx-fs-sm">
                {loading ? (
                    <div className="wy-pl-animate-pulse wy-loading-description-label-120"></div>
                ) : (
                    label
                )}
            </div>
            <div className="description-value gx-fs-lg gx-text-black">
                {loading ? (
                    <div className="wy-pl-animate-pulse wy-loading-description-label-80"></div>
                ) : (
                    value
                )}
            </div>
        </div>
    );
    const getSpPayoutConfig = () => {
        const sp_payouts_config = spConfigData.srvc_type_sp_payouts_config;
        if (!sp_payouts_config) {
            return [];
        }

        // Parse the top-level config.
        const parsedConfig = JSON.parse(sp_payouts_config);
        const dynamicKey = Object.keys(parsedConfig)[0];
        const config = parsedConfig[dynamicKey];
        return config;
    };

    const getSpPayoutStaticColumns = (payoutConfig) => {
        const staticColumns = [];
        // if (isVendorPayouts && !includeOnlyNumbers) {
        staticColumns.push({
            label: 'Vendor',
            value: 'vendor',
            key: 'vendor',
            render: (text, record) => {
                console.log('record', record);
                return <UserNameById id={text} customError="-" />;
            },
        });
        // }
        // Use the defaults if labels are not provided.
        staticColumns.push({
            key: 'total',
            value: 'total',
            label: payoutConfig?.total_field_label || 'Total',
        });

        staticColumns.push({
            key: 'rate',
            value: 'rate',
            label: payoutConfig?.price_field_label || 'Price',
        });

        staticColumns.push({
            key: 'qty',
            value: 'qty',
            label: payoutConfig?.quantity_field_label || 'Quantity',
        });
        return staticColumns;
    };

    /**
     * Retrieves vendor cost columns based on the provided srvc_type_sp_payouts_config.
     */
    const getSpPayoutVenderCostColumns = () => {
        const payoutConfig = getSpPayoutConfig();

        // Add static columns options including number fields
        const staticColumns = getSpPayoutStaticColumns(payoutConfig);

        // get all custom fields
        const parsedFields = payoutConfig.fields
            ? JSON.parse(payoutConfig.fields)
            : {};

        const translatedFields = parsedFields.translatedFields || [];
        const customColumnFields = translatedFields.map((field) => ({
            key: field?.key,
            value: field?.key,
            label: field?.label,
        }));

        const allSpPayoutColunms = [...staticColumns, ...customColumnFields];
        return allSpPayoutColunms;
    };

    const getVendorCostColMeta = () => {
        let vendorCol = [];
        let expectedCostCol;
        let actualCostCols = [];
        // let filteredVendorCostCol;
        const allVendorCostColumn = getSpPayoutVenderCostColumns();
        const getConfiguredVendorColumn =
            spConfigData.project_profit_loss_vendor_cost_columns;
        const actualCost =
            spConfigData?.project_profit_loss_vendor_actual_cost_column || [];
        const expectedCost =
            spConfigData?.project_profit_loss_vendor_expected_cost_column || [];

        // Filter columns based on configuration
        let filteredVendorCostCol = [];

        // Add configured columns
        allVendorCostColumn.forEach((item) => {
            if (getConfiguredVendorColumn.includes(item.value)) {
                filteredVendorCostCol.push({ ...item });
            }

            // Add actual cost column
            if (actualCost.includes(item.value)) {
                filteredVendorCostCol.push({
                    ...item,
                    type: 'actual',
                    // isActualCost: true,
                    // uniqueKey: `${item.key}_actual`,
                });
            }

            // Add expected cost column
            if (expectedCost.includes(item.value)) {
                filteredVendorCostCol.push({
                    ...item,
                    type: 'expected',
                    // isExpectedCost: true,
                    // uniqueKey: `${item.key}_expected`,
                });
            }
        });

        filteredVendorCostCol.forEach((item) => {
            const payoutConfig = getSpPayoutConfig();
            const fields = payoutConfig.fields
                ? JSON.parse(payoutConfig.fields).translatedFields
                : [];
            const field = fields.find((f) => f.key === item.key);

            const column = {
                ...item,
                title: item.label,
                dataIndex: item.key,
                key: item.key,
            };

            // Add actual_cost and expected_cost keys
            if (actualCost.includes(item.value) && item.type === 'actual') {
                column.title = 'ACTUAL COST';
                column.actual_cost = true;
                column.costKey = 'actual_cost';
                column.key = 'actual_cost';
                column.fixed = 'right';
                column.width = 130;
            }
            if (expectedCost.includes(item.value) && item.type === 'expected') {
                column.title = 'EXPECTED COST';
                column.expected_cost = true;
                column.costKey = 'expected_cost';
                column.key = 'expected_cost';
            }

            column.render = (text, record) => {
                if (item.key === 'vendor' && text) {
                    return <UserNameById id={text} />;
                }
                if (
                    field?.widget === 'select' ||
                    field?.widget === 'radio-group' ||
                    field?.widget === 'checkbox-group'
                ) {
                    return getLabelFrmOptionsValue(field.options, text);
                }
                if (field?.widget === 'date-picker') {
                    return moment(text).format('YYYY-MM-DD');
                }
                // Format cost values
                if (column.actual_cost || column.expected_cost) {
                    const rawValue = record[column.costKey] || text;
                    const value = parseFloat(rawValue);
                    return isNaN(value)
                        ? '₹0.00'
                        : value < 0
                          ? `- ₹${formatNumber(Math.abs(value))}`
                          : `₹${formatNumber(value)}`;
                }
                return text;
            };
            // Push actual cost columns to separate array
            if (column.actual_cost) {
                actualCostCols.push(column); // Only 1 actual cost expected
            } else if (column.expected_cost) {
                expectedCostCol = column; // Only 1 expected cost expected
            } else {
                vendorCol.push(column);
            }
        });
        // Insert expected cost before actual cost
        if (expectedCostCol) {
            vendorCol.push(expectedCostCol);
        }

        vendorCol = vendorCol.concat(actualCostCols);

        return vendorCol;
    };

    const showAllAuthoritiesUI = () => {
        return (
            <div className="scrolling_wrapper gx-mr-3">
                {getAuthorityWithRoles()?.length > 0 &&
                    getAuthorityWithRoles()?.map((authority: Authority) => (
                        <Tooltip
                            title={() => {
                                return (
                                    <UserNameById
                                        id={srvcReqData[authority.key]}
                                        customError={'Missing'}
                                    />
                                );
                            }}
                            placement="top"
                        >
                            <div className="gx-text-center gx-d-inline-block">
                                <Tag
                                    color={`${getRandomTagBgColor()}`}
                                    className="wy-tag-line-height-adjust gx-mb-0"
                                >
                                    <UserNameById
                                        id={srvcReqData[authority.key]}
                                        customError={'-'}
                                    />
                                    <br />
                                    <small className="gx-mb-0 gx-text-center">
                                        {authority.label}
                                    </small>
                                </Tag>
                            </div>
                        </Tooltip>
                    ))}
            </div>
        );
    };
    // Add this function to calculate total vendor cost
    const getTotalVendorCost = (rowData) => {
        const actualCost =
            spConfigData?.project_profit_loss_vendor_actual_cost_column || [];

        if (!actualCost.length) return 0;

        let total = 0;
        rowData.forEach((row) => {
            const value = parseFloat(row[actualCost]) || 0;
            total += value;
        });

        return total;
    };

    const getTotalInhouseCost = (rowData) => {
        let total = 0;
        rowData.forEach((row) => {
            const value = parseFloat(row.actual_cost) || 0;
            total += value;
        });
        return total;
    };

    const getSubtotalOfRevenue = (rowData) => {
        if (!rowData || !rowData.length) return 0;

        let total = 0;
        rowData.forEach((row) => {
            const subTotal = parseFloat(row.sub_total_after_discount) || 0;
            total += subTotal;
        });
        return total;
    };

    const formatNumber = (value: number): string => {
        if (!value) return '0';

        const rounded = Number(value.toFixed(2));
        return Number.isInteger(rounded)
            ? rounded.toString()
            : rounded.toFixed(2);
    };

    const isVendorCostDataPresent = () => {
        return (
            viewData?.vendorCostData &&
            viewData.vendorCostData.some((item) => Object.keys(item).length > 0)
        );
    };

    const isPrvdrBillingEnable = () => {
        return spConfigData?.srvc_type_enable_billing;
    };

    const isInhouseCostConfigured =
        spConfigData?.project_profit_loss_user_inhouse_identity;
    const isVendorCostConfigured =
        spConfigData?.project_profit_loss_vendor_cost_columns?.length > 0;
    const isPayout = spConfigData?.enable_sp_payouts;

    const vendorcost = getTotalVendorCost(viewData?.vendorCostData || []);
    const inhousecost = getTotalInhouseCost(
        viewData?.technician_data_fr_cost_breakdown || []
    );
    const revenueSubtotal = getSubtotalOfRevenue(
        viewData?.revenueBreakdownData || []
    );
    const totalCost = inhousecost + vendorcost;
    const discount = viewData?.additionalDiscount || 0;
    const totalRevenue = revenueSubtotal - discount;
    return (
        <div className="wy-pnl-wrapper">
            {loading && (
                <div className="gx-mb-4">
                    <SkeletonLoader
                        box={true}
                        gridBox={isMobileView() ? 2 : 4}
                    />
                    {isMobileView() && (
                        <SkeletonLoader box={true} gridBox={2} />
                    )}
                </div>
            )}

            {!loading && showProfit && (
                <>
                    <Row gutter={[10, 10]}>
                        <Col md={6} sm={12} xs={12}>
                            <div className="gx-w-100 wy-pl-wrapper-card gx-mb-0">
                                <div className="wy-pL-card-border"></div>
                                <div className="gx-text-gray gx-fs-sm gx-mb-2">
                                    GM%
                                </div>
                                <div
                                    className={`${totalRevenue - totalCost > 0 ? 'gx-text-green' : 'gx-text-red'} gx-fs-xl`}
                                >
                                    {totalRevenue === 0
                                        ? '0%'
                                        : `${formatNumber(((totalRevenue - totalCost) / totalRevenue) * 100)}%`}
                                </div>
                            </div>
                        </Col>
                        <Col md={6} sm={12} xs={12}>
                            <div className="gx-w-100 wy-pl-wrapper-card gx-mb-0">
                                <div className="wy-pL-card-border"></div>
                                <div className="gx-text-gray gx-fs-sm gx-mb-2">
                                    Net GM
                                </div>
                                <div
                                    className={`${totalRevenue - totalCost > 0 ? 'gx-text-green' : 'gx-text-red'} gx-fs-xl`}
                                >
                                    {totalRevenue - totalCost < 0
                                        ? `- ₹${formatNumber(Math.abs(totalRevenue - totalCost))}`
                                        : `₹${formatNumber(
                                              totalRevenue - totalCost
                                          )}`}
                                </div>
                            </div>
                        </Col>
                        <Col md={6} sm={12} xs={12}>
                            <div className="gx-w-100 wy-pl-wrapper-card gx-mb-0">
                                <div className="wy-pL-card-border"></div>
                                <div className="gx-text-gray gx-fs-sm gx-mb-2">
                                    Total Revenue
                                </div>
                                <div className="gx-fontweight-bold gx-text-black gx-fs-xl">
                                    {totalRevenue < 0
                                        ? `- ₹${formatNumber(Math.abs(totalRevenue))}`
                                        : `₹${formatNumber(totalRevenue)}`}
                                </div>
                            </div>
                        </Col>
                        <Col md={6} sm={12} xs={12}>
                            <div className="gx-w-100 wy-pl-wrapper-card gx-mb-0">
                                <div className="wy-pL-card-border"></div>
                                <div className="gx-text-gray gx-fs-sm gx-mb-2">
                                    Total Cost
                                </div>
                                <div className="gx-fontweight-bold gx-text-black gx-fs-xl">
                                    {totalCost < 0
                                        ? `- ₹${formatNumber(Math.abs(totalCost))}`
                                        : `₹${formatNumber(totalCost)}`}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </>
            )}

            <div className="gx-mt-4">
                <div className="wy-pl-wrapper-card gx-mt-4">
                    <div className="wy-request-details-wrapper">
                        <p className="gx-fs-18 gx-text-black">
                            Request Details
                        </p>
                        <div className="wy-request-details-grid">
                            {requestDetailsUI({
                                label: 'Vertical',
                                value:
                                    spConfigData && spConfigData.vertical_title
                                        ? spConfigData.vertical_title
                                        : '-',
                            })}
                            {requestDetailsUI({
                                label: 'Location Group',
                                value: locGrpsName || '-',
                            })}
                            {requestDetailsUI({
                                label: 'Order Date',
                                value:
                                    srvcReqData &&
                                    srvcReqData.latestOpenStatus &&
                                    srvcReqData.latestOpenStatus.time
                                        ? convertUTCToDisplayTime(
                                              srvcReqData.latestOpenStatus.time
                                          )
                                        : '-',
                            })}
                            {requestDetailsUI({
                                label: 'Completion Date',
                                value:
                                    srvcReqData &&
                                    srvcReqData.latestClosedStatus &&
                                    srvcReqData.latestClosedStatus.time
                                        ? convertUTCToDisplayTime(
                                              srvcReqData.latestClosedStatus
                                                  .time
                                          )
                                        : '-',
                            })}
                        </div>
                    </div>
                </div>
                <div className="wy-pl-wrapper-card">
                    {getAuthorityWithRoles() &&
                    getAuthorityWithRoles().length === 0 ? (
                        <div>
                            <Alert
                                message="No Authorities selected for this request"
                                type="warning"
                                showIcon
                                className="gx-mb-0"
                            />
                        </div>
                    ) : (
                        <div>
                            <p className="gx-fs-18 gx-text-black">
                                Authorities
                            </p>
                            {loading ? (
                                <div className="gx-mb-2">
                                    <SkeletonLoader box={true} />
                                </div>
                            ) : (
                                showAllAuthoritiesUI()
                            )}
                        </div>
                    )}
                </div>
            </div>
            {isPrvdrBillingEnable() && (
                <div className="gx-mt-3 gx-mb-2 wy-pl-wrapper-card">
                    <p className="gx-fs-18 gx-mb-1">Revenue Breakdown</p>
                    <div className="wy-extra-table-wrapper">
                        {loading ? (
                            <div className="gx-mb-4">
                                {/* @ts-ignore */}
                                <TableLoading
                                    cols={6}
                                    rows={3}
                                    mobile={{ cols: 3, rows: 5 }}
                                    fixed="both"
                                ></TableLoading>
                            </div>
                        ) : (
                            <div className="table-responsive-wy wy-pl-table-style  wy-pl-table-style-border2  wy-pl-td-lc-text-right">
                                <Table
                                    columns={revenueFixedBreakdownCols}
                                    dataSource={
                                        viewData?.revenueBreakdownData || []
                                    }
                                    pagination={{
                                        pageSize: 5,
                                        size: 'small',
                                    }}
                                    scroll={{
                                        x: 1500,
                                    }}
                                />
                            </div>
                        )}
                        {loading ? (
                            <div className="gx-mb-4">
                                {/* @ts-ignore */}
                                <Skeleton.Input
                                    className="wy_skeleton_loading_box"
                                    active
                                />
                            </div>
                        ) : (
                            <div className=" gx-mt-2 gx-text-right">
                                {/* Subtotal */}
                                <div className="wy-mkw-table-footer-data">
                                    <div className="">Subtotal</div>
                                    <div className="">
                                        ₹{formatNumber(revenueSubtotal)}
                                    </div>
                                </div>

                                {/* Discount - Value from Additional discount */}
                                <div className="wy-mkw-table-red wy-mkw-table-footer-data">
                                    <div className="">Discount</div>
                                    <div className="">
                                        ₹{formatNumber(discount)}
                                    </div>
                                </div>

                                {/* Total Revenue = Actual Revenue - Discount */}
                                <div className="wy-mkw-table-tota-bg wy-mkw-table-blue-bg gx-fs-18 wy-mkw-table-footer-data">
                                    <div className="">Total Revenue</div>
                                    <div className="">
                                        {totalRevenue < 0
                                            ? `- ₹${formatNumber(Math.abs(totalRevenue))}`
                                            : `₹${formatNumber(totalRevenue)}`}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
            {((isInhouseCostConfigured &&
                viewData?.technician_data_fr_cost_breakdown.length > 0) ||
                (isVendorCostConfigured &&
                    isPayout &&
                    isVendorCostDataPresent())) && (
                <div className="gx-mt-3 gx-mb-2 wy-pl-wrapper-card">
                    <p className="gx-fs-18 gx-mb-1 gx-font-weight-bold">
                        Cost Breakdown
                    </p>

                    {isInhouseCostConfigured &&
                        viewData?.technician_data_fr_cost_breakdown.length >
                            0 && (
                            <>
                                <p className="gx-fs-18 gx-mb-1">Inhouse Cost</p>
                                <div className="wy-extra-table-wrapper">
                                    <div>
                                        {loading ? (
                                            <div className="gx-mb-4">
                                                {/* @ts-ignore */}
                                                <TableLoading
                                                    cols={5}
                                                    rows={3}
                                                    mobile={{
                                                        cols: 3,
                                                        rows: 5,
                                                    }}
                                                    // fixed="both"
                                                ></TableLoading>
                                            </div>
                                        ) : (
                                            <div className="table-responsive-wy wy-table-footer-wrapper wy-pl-table-style-border wy-pl-table-style  wy-pl-td-lc-text-right">
                                                <Table
                                                    columns={
                                                        inHouseCostbreakdownCols
                                                    }
                                                    dataSource={
                                                        viewData?.technician_data_fr_cost_breakdown ||
                                                        []
                                                    }
                                                    pagination={{
                                                        pageSize: 5,
                                                        size: 'small',
                                                    }}
                                                    scroll={{
                                                        x: 1500,
                                                    }}
                                                />
                                            </div>
                                        )}
                                        {loading ? (
                                            <div className="gx-mb-4">
                                                {/* @ts-ignore */}
                                                <Skeleton.Input
                                                    className="wy_skeleton_loading_box"
                                                    active
                                                />
                                            </div>
                                        ) : (
                                            <div className="gx-mt-2 gx-text-right">
                                                <div className="wy-mkw-table-tota-bg wy-mkw-table-blue-bg wy-mkw-table-footer-data">
                                                    <div className="">
                                                        Total Inhouse Cost
                                                    </div>
                                                    <div className="">
                                                        ₹
                                                        {formatNumber(
                                                            inhousecost
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </>
                        )}
                    {isVendorCostConfigured &&
                        isPayout &&
                        isVendorCostDataPresent() && (
                            <>
                                <p className="gx-fs-18 gx-mb-1 gx-mt-3">
                                    Vendor Cost
                                </p>
                                <div className="wy-extra-table-wrapper">
                                    {loading ? (
                                        <div className="gx-mb-4">
                                            {/* @ts-ignore */}
                                            <TableLoading
                                                cols={6}
                                                rows={3}
                                                mobile={{
                                                    cols: 3,
                                                    rows: 5,
                                                }}
                                                fixed="right"
                                            ></TableLoading>
                                        </div>
                                    ) : (
                                        <div className="table-responsive-wy wy-table-footer-wrapper  wy-pl-table-style-border wy-pl-table-style  wy-pl-td-lc-text-right">
                                            <Table
                                                className="wy-vendor-cost-table-w-adjust"
                                                columns={getVendorCostColMeta()}
                                                dataSource={
                                                    viewData?.vendorCostData ||
                                                    []
                                                }
                                                pagination={false}
                                                scroll={{
                                                    x: 1500,
                                                }}
                                            />
                                        </div>
                                    )}
                                    {loading ? (
                                        <div className="gx-mb-4">
                                            {/* @ts-ignore */}
                                            <Skeleton.Input
                                                className="wy_skeleton_loading_box"
                                                active
                                            />
                                        </div>
                                    ) : (
                                        <div className="gx-mt-2 gx-text-right">
                                            <div className="wy-mkw-table-tota-bg wy-mkw-table-blue-bg wy-mkw-table-footer-data">
                                                <div className="">
                                                    Total Vendor Cost
                                                </div>
                                                <div className="">
                                                    {vendorcost < 0
                                                        ? `- ₹${formatNumber(Math.abs(vendorcost))}`
                                                        : `₹${formatNumber(
                                                              vendorcost
                                                          )}`}{' '}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    {(viewData
                                        ?.technician_data_fr_cost_breakdown
                                        .length > 0 ||
                                        isVendorCostDataPresent()) && (
                                        <>
                                            {loading ? (
                                                <div className="gx-mb-4">
                                                    {/* @ts-ignore */}
                                                    <Skeleton.Input
                                                        className="wy_skeleton_loading_box"
                                                        active
                                                    />
                                                </div>
                                            ) : (
                                                <div className=" wy-mkw-table-footer-data gx-fs-18 gx-mt-4 gx-text-right wy-pnl-total-cost">
                                                    <div>Total Cost</div>{' '}
                                                    <div className="">
                                                        {totalCost < 0
                                                            ? `- ₹${formatNumber(Math.abs(totalCost))}`
                                                            : `₹${formatNumber(totalCost)}`}
                                                    </div>
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                            </>
                        )}
                </div>
            )}
        </div>
    );
};

export default ProjectProfitAndLoss;