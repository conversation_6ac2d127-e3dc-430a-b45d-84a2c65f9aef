import React, { Component } from 'react';
import { Modal, Tabs } from 'antd';
import http_utils from '../../util/http_utils';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import { getNewSubTaskFormMeta } from '../../components/WIFY/subtasks/helpers';
import ConfigHelpers from '../../util/ConfigHelpers';
import LamdaForBulkAssign from './LamdaForBulkAssign';
import BulkAssignLineItems from './BulkAssignLineItems';

const protoUrl = '/subtasks/proto';
const submitUrl = '/subtasks/sbtsk_bulk_assign';
const assignAuthorityUrl = '/services/modify';
class BulkAssign extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.initApiUrls(this.props);
    }

    state = {
        render_helper: false,
        visible: this.props.showEditor || false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
    };

    initApiUrls(props) {
        let is_bulk_update = true;
        this.submitUrl =
            submitUrl +
            '/' +
            props.srvcDetails?.srvc_id +
            '/' +
            (this.props.isCustAcces ? '1' : '0');

        this.assignAuthorityUrl =
            assignAuthorityUrl +
            '/' +
            props.srvcDetails?.srvc_id +
            '/' +
            (this.props.isCustAcces ? '1' : '0') +
            '/' +
            (is_bulk_update ? '1' : '0');
    }

    componentDidMount() {
        this.initViewData();
    }

    initViewData() {
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {
                srvc_type_id: this.props.srvcDetails?.srvc_id,
            };
            const onComplete = (resp) => {
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };

            http_utils.performGetCall(protoUrl, params, onComplete, onError);
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                    }
                }
            );
        }
    }

    handleOk = () => {
        this.setState({
            visible: false,
            isFormSubmitting: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }

    tellParentToRefreshList(entry_id) {
        // console.log("Trying to to tell parent to refresh list");
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    getSubTaskTypes() {
        let sbtaskType = [];
        let isServiceProvider = ConfigHelpers.isServiceProvider();
        if (isServiceProvider) {
            sbtaskType = this.props.sbtaskTypeList;
        } else {
            let srvcApplSubTaskIds =
                this.props.srvcConfigData?.srvc_appl_sub_tasks;
            let sbtskTypeList = this.props.sbtaskTypeList;
            if (srvcApplSubTaskIds) {
                srvcApplSubTaskIds.map((singleSbtsk) => {
                    let sbtaskTypeData = sbtskTypeList.filter(
                        (item) => item.value == singleSbtsk
                    )[0];
                    if (sbtaskTypeData) {
                        sbtaskType.push(sbtaskTypeData);
                    }
                });
            }
        }
        return sbtaskType;
    }

    getSbtskFieldsMeta = () => {
        const sbtskMeta = [];
        let sbtaskMetaParams = {
            formRef: this.formRef,
            sbtskDetails: this.props,
            forBulkAssign: true,
        };
        let sbtaskFormMeta = getNewSubTaskFormMeta(sbtaskMetaParams).fields;
        if (sbtaskFormMeta) {
            let newTitles = {
                sbtsk_assignee: 'Assignee',
                sbtsk_priority: 'Priority',
                sbtsk_start_day: 'Start date',
                sbtsk_start_time: 'Start Time',
                sbtsk_end_time: 'End Time',
                sbtsk_remarks: 'Remarks',
            };
            sbtaskFormMeta.map((singleFormMeta) => {
                let modifiedMeta = { ...singleFormMeta };
                if (newTitles.hasOwnProperty(singleFormMeta.key)) {
                    modifiedMeta.label = newTitles[singleFormMeta.key];
                    sbtskMeta.push(modifiedMeta);
                }
            });
        }
        let newSbtskobj = [
            {
                key: 'sbtsk_ticket_id',
                label: 'Ticket ID',
                required: true,
                colSpan: 4,
            },
            {
                key: 'sbtsk_type_id',
                label: 'Subtask type',
                required: true,
                widget: 'select',
                options: this.getSubTaskTypes(),
                colSpan: 4,
            },
        ];
        let meta = [...newSbtskobj, ...sbtskMeta];
        return meta;
    };

    getAuthorityAssignMeta = () => {
        const basicFields = [
            {
                key: 'tms_display_code',
                label: 'Ticket ID',
                required: true,
                colSpan: 4,
            },
        ];
        const authorityFields =
            this.state.viewData?.role_wise_authorities_users_list || [];

        return [...basicFields, ...authorityFields];
    };

    hasBulkAssignLambda = () => {
        let automate_dep_settings =
            this.state.viewData?.automate_dep_settings?.form_data;
        let srvcTypeId = this.props.srvcDetails?.srvc_id;
        if (automate_dep_settings && srvcTypeId > 0) {
            const lambdaArnForThisSrvcType =
                automate_dep_settings[`auto_dep_lambda_arn_for_${srvcTypeId}`];
            // console.log('lambdaArnForThisSrvcType',lambdaArnForThisSrvcType)
            if (lambdaArnForThisSrvcType) {
                return true;
            }
        }
        return false;
    };

    render() {
        const { isFormSubmitting, visible } = this.state;
        const editorTitle = 'Bulk Assign';
        return visible ? (
            <Modal
                title={
                    <span>
                        <i className={` gx-mr-2 h1`}></i> {editorTitle}
                    </span>
                }
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={900}
                style={{
                    marginTop: '-70px',
                }}
                bodyStyle={{
                    padding: '18px',
                    paddingTop: '0px',
                }}
                footer={null}
                onCancel={this.handleCancel}
            >
                <Tabs defaultActiveKey="basic" className="gx-mb-2">
                    {this.props.isSrvcTypeNatureProject &&
                        !this.props.isCustAcces && (
                            <Tabs.TabPane
                                tab="Line items"
                                key="line_items"
                                forceRender={true}
                            >
                                <>
                                    <BulkAssignLineItems
                                        {...this.props}
                                        isCustAcces={this.props.isCustAcces}
                                        tellParentToRefreshList={(entry_ids) =>
                                            this.tellParentToRefreshList(0)
                                        }
                                    />
                                </>
                            </Tabs.TabPane>
                        )}
                    {!this.props.isSrvcTypeNatureProject && (
                        <Tabs.TabPane
                            tab="Assignees"
                            key="assignees"
                            forceRender={true}
                        >
                            <>
                                {this.hasBulkAssignLambda() && (
                                    <LamdaForBulkAssign {...this.props} />
                                )}
                                <BulkUploader
                                    // demoMode
                                    // renderFormsForRows
                                    // debugMode
                                    onDataModified={(entry_ids) =>
                                        this.tellParentToRefreshList(0)
                                    }
                                    submitUrl={this.submitUrl}
                                    dataProto={this.getSbtskFieldsMeta()}
                                    timeFormatMsg
                                    isBulkAssignComp
                                />
                            </>
                        </Tabs.TabPane>
                    )}
                    {!this.props.isSrvcTypeNatureProject &&
                        !this.props.isCustAcces && (
                            <Tabs.TabPane
                                tab="Authorities"
                                key="authorities"
                                forceRender={true}
                            >
                                <BulkUploader
                                    // demoMode
                                    // renderFormsForRows
                                    // debugMode
                                    bulkUpdateAuthority
                                    onDataModified={(entry_ids) =>
                                        this.tellParentToRefreshList(0)
                                    }
                                    submitUrl={this.assignAuthorityUrl}
                                    dataProto={this.getAuthorityAssignMeta()}
                                    timeFormatMsg
                                />
                            </Tabs.TabPane>
                        )}
                </Tabs>
            </Modal>
        ) : (
            <></>
        );
    }
}

export default BulkAssign;
