import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import DailyReqStatusEditor, { onFilesChangedFn } from './DailyReqStatusEditor';
import ConfigHelpers from '../../../../util/ConfigHelpers';

jest.mock('../../../../util/http_utils');
jest.mock('../../../../components/wify-utils/S3Uploader/S3Uploader');

jest.mock('../../../../util/ConfigHelpers');
// Mocking the external dependencies
const setFilesBySection = jest.fn();
const onFormValueChange = jest.fn();

const prefix = 'DailyReqStatusEditor';

const editorDayDetails = {
    assignee_date: '2024-08-09',
    assignee_details: [
        {
            day: '2024-08-09',
            end_time: null,
            attendance: 'Absent',
            sbtsk_type: {
                icon: 'icon-map-directions',
                title: 'Visit',
                value: 2,
            },
            start_time: null,
            user_roles: ['Admin'],
            assignee_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            subtsk_db_id: 471,
            assignee_code: null,
            assignee_name: 'Shambhu Choudhary',
            sbtsk_type_id: 2,
            srvc_req_details: {
                id: 388,
                title: 'HMLT240809949501',
                c_time: '2024-08-09T09:12:33.916391',
                labels: null,
                org_id: 3,
                status: 'open',
                address: '400068, Mumbai, MAHARASHTRA',
                priority: 'Normal',
                cust_name: 'Shambhu choudhary',
                description: 'Ticket 2',
                srvc_type_id: 15,
                srvc_prvdr_id: 2,
            },
            is_assignee_authority: false,
        },
    ],
};

const srvcConfigData = {
    qty: '',
    rate: '',
    total: '',
    org_id: 3,
    usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
    entry_id: 15,
    ip_address: '::1',
    sbtsk_fr_3: 2,
    user_agent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    sbtsk_fr_115: 2,
    sbtsk_fr_117: 3,
    sbtsk_fr_118: 3,
    srvc_statuses:
        '{"ACTIVE":[{"key":"open","color":"#e91e63","title":"Open","status_type":"ACTIVE"},{"key":"u6v6TFg5","color":"#ffc107","title":"Scheduled","status_type":"ACTIVE"}],"DONE":[{"key":"FSPiwnpL","color":"#fa8c16","title":"Completed","status_type":"DONE"},{"key":"pmqxLWhI","color":"#009688","title":"Visited","status_type":"DONE"}],"CLOSED":[{"key":"closed","color":"#607d8b","title":"Closed","status_type":"CLOSED"}]}',
    srvc_type_key: 'tickets',
    srvc_type_desc: 'Complaint with respect to previously done job',
    srvc_type_name: 'Tickets',
    srvc_authorities: [115, 117, 3],
    srvc_type_nature: 'project_based',
    srvc_type_prefix: 'HMLT',
    srvc_can_cust_rate: true,
    gai_rating_subtasks: true,
    srvc_appl_sub_tasks: [3, 2],
    srvc_possible_prvdrs: [2],
    srvc_cust_fields_json:
        '{"originalFields":[{"id":"899384e6-31d1-4c6e-a7b6-9a666efb2a01","element":"Dropdown","label":{"blocks":[{"key":"d2u6r","text":"Size Category","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"adcc5084-7041-4b84-8c82-c363074420da","value":"Small"},{"id":"64444a30-85a6-48c1-8c00-dc8ed30646a4","value":"Medium"},{"id":"8bbcf7f9-1d81-4ad1-aa0e-6557fb4a3609","value":"Large"},{"id":"86c4de3d-3c77-4b30-9004-65f1d432c1cf","value":"XL"}]},{"id":"15f81209-b9e0-4d00-8bbe-fa728f92c24f","element":"Tags","required":false,"label":{"blocks":[{"key":"2bsg2","text":"Products","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"7a0ee77b-8b5c-4eba-9253-4b0d0884ce86","label":"Bedroom Accessories","value":"Value1"},{"id":"b986f5fa-9aca-4446-8670-a5e4e73857c2","label":"Bedside Tables","value":"Value2"},{"id":"8043058c-0ee8-4236-a42f-08edfebdfcef","label":"Chest Of Drawers","value":"Value3"},{"id":"bd949710-08b2-48e6-b1d3-b488427494ab","label":"Chests","value":"Value4"},{"id":"6cc10d35-e2a8-40dd-a25c-f31d5f29e8ee","label":"Cupboards","value":"Value5"},{"id":"b6dc46cc-5c9c-4797-a7d1-21dc8b8623d8","label":"Dressers","value":"Value6"},{"id":"61bfc4b9-9927-41b0-b201-3661ff829f2d","label":"Mirrors","value":"Value7"},{"id":"242a6701-8e8d-4c22-afa3-45c6ebf70f53","label":"Non-Storage Beds","value":"Value8"},{"id":"7ebe5237-a234-451e-92c2-eea11a2dc720","label":"Single Beds","value":"Value9"},{"id":"a41b2896-37e3-4554-ab8a-e8891258bb8a","label":"Storage Beds","value":"Value10"},{"id":"ab704565-5520-447a-8bf5-40aa6ecc9912","label":"Hydraulic Beds","value":"Value11"},{"id":"eafca0ef-6b90-4de1-8d2f-ca474b4461ad","label":"4 Seater Table","value":"Value12"},{"id":"ded1f5fe-49b8-45d1-b512-839843a60902","label":"6 Seater Table","value":"Value13"},{"id":"83129ba8-3107-4092-af2b-49acfc82fdfb","label":"Dining Bench","value":"Value14"},{"id":"87964be1-27f6-4e8e-b76f-313b32d06f2e","label":"Dining Chairs","value":"Value15"},{"id":"1c818bf1-996b-4526-a2f3-14e8ef48186e","label":"Extendable Table","value":"Value16"},{"id":"bfb21e3b-b5bb-44c4-92fd-bf6c55b06194","label":"Bar Stools","value":"Value17"},{"id":"bb37dbee-90de-4c2b-8a4f-0cfda542db27","label":"Bar Units","value":"Value18"},{"id":"6dbac22b-ceb2-4077-a758-61b7e91a3c88","label":"Dining Accessories","value":"Value19"},{"id":"cdaf60b7-7f6c-458d-9e9f-46d1c1d91efb","label":"Kitchen Racks","value":"Value20"},{"id":"eb609968-e027-4cf3-8fae-4b35848fb08a","label":"Sideboards","value":"Value21"},{"id":"8868845c-7b06-4c03-bb2e-fa1517904d6f","label":"Bookshelf","value":"Value22"},{"id":"5be4049d-7f78-4bab-aefc-f8aa67609afd","label":"Coffee Tables","value":"Value23"},{"id":"a0b40fce-661b-4915-adf4-3080fb479c61","label":"Display Cabinets","value":"Value24"},{"id":"4a548d2a-5897-43a3-916e-21b17ecb730f","label":"Entryway","value":"Value25"},{"id":"fc75ec8c-97d6-444d-a8f5-1c95c4bf1fda","label":"Laptop Tables","value":"Value27"},{"id":"1c62995b-9bab-4f75-8889-daf7dbd7ced5","label":"Prayer Units","value":"Value28"},{"id":"609a7667-8115-445c-b562-a7bcd730c4bb","label":"Shoe Racks","value":"Value29"},{"id":"c4412ce9-d7b7-4265-9f53-357be291339e","label":"Side Tables","value":"Value30"},{"id":"3a8caa47-cb17-404a-9605-124097817fd1","label":"Tv Unit","value":"Value31"},{"id":"80ee4053-6693-48d0-a088-69762ccc8aa5","label":"Tv Units","value":"Value32"},{"id":"3740593e-ddf8-49c8-9546-feb5c9aed483","label":"Wall Shelf","value":"Value33"},{"id":"8e783802-70ea-4191-8f53-5e20f11ba8c4","label":"Wardrobes","value":"Value34"},{"id":"082e8fbf-d198-4e40-b4d5-0653faa4f712","label":"Accent Chairs","value":"Value35"},{"id":"e13ae7a4-1378-4d6b-840a-47c0da93f643","label":"Benches","value":"Value36"},{"id":"8b3e0a9e-da7e-430a-a96c-e12591e0dddc","label":"Day Beds","value":"Value37"},{"id":"096e53fe-d41f-49d5-8fac-4c76b3cf02ed","label":"Lounge Chairs","value":"Value38"},{"id":"95445b37-c5a0-4dcf-bb9e-04b830c77c15","label":"Loveseats","value":"Value39"},{"id":"f933e657-83f5-4355-9b31-95cfa9c0dc0f","label":"Ottomans","value":"Value40"},{"id":"bf0dd3d9-67be-438c-8049-fac74b04635d","label":"Recliners","value":"Value41"},{"id":"72b3feee-8212-4cc8-aea2-82ac1c3ac5a3","label":"Sofa Beds","value":"Value42"},{"id":"f302f1cc-1bc5-493b-b299-0433116dffe8","label":"Mattress","value":"Value43"},{"id":"4efd37a4-5eb9-4213-a3ef-bb27b41049bb","label":"Balcony Chairs","value":"Value44"},{"id":"978882b6-df13-49b4-8011-fc514b20431a","label":"Balcony Swings","value":"Value45"},{"id":"892b2e97-3089-4d14-b1da-2dd2ad006203","label":"Balcony Tables","value":"Value46"},{"id":"14aa43e8-c211-4027-a75b-b86f88fd2a77","label":"Fabric Sofa","value":"Value47"},{"id":"12c2709e-2034-42d6-8169-30b1c7408726","label":"Leather Sofa","value":"Value48"},{"id":"36ef55b3-7cb6-42f0-ac92-c28af3368138","label":"Leatherette Sofa","value":"Value49"},{"id":"d17e95e1-1f9a-4ea6-8702-580a51ecfba7","label":"Recliner Sofa","value":"Value50"},{"id":"92d8a9f2-aa8b-44b4-9ed4-40d8785251d8","label":"Wooden Sofa","value":"Value51"},{"id":"8ba96d59-2090-4121-80a5-bd3b11b75a7b","label":"Study Chairs","value":"Value52"},{"id":"4f9c89e4-286e-49e7-bb1a-3386308df315","label":"Study Tables","value":"Value53"}]},{"id":"447e74f0-41c6-4c81-baf9-e9b3c0642b8f","element":"Date","label":{"blocks":[{"key":"3v3h2","text":"Delivery Date","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"fcd2b6c1-170b-40bd-bf4a-444f93ac8f85","element":"TextInput","required":false,"label":{"blocks":[{"key":"1m6uh","text":"ORDER_NO","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":0,"length":8,"style":"color-rgb(0,0,0)"},{"offset":0,"length":8,"style":"bgcolor-rgb(255,255,255)"},{"offset":0,"length":8,"style":"fontsize-13"},{"offset":0,"length":8,"style":"fontfamily-Roboto, RobotoDraft, Helvetica, Arial, sans-serif"}],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"960b442b-4305-48ca-a907-1c04c1e640af","element":"NumberInput","required":false,"label":{"blocks":[{"key":"3ne15","text":"Qty","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"4ea21a95-2739-4433-9610-9f5f47eec5d7","element":"NumberInput","required":false,"label":{"blocks":[{"key":"1f17p","text":"NO_OF_BOXES","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":0,"length":11,"style":"color-rgb(0,0,0)"},{"offset":0,"length":11,"style":"bgcolor-rgb(255,255,255)"},{"offset":0,"length":11,"style":"fontsize-13"},{"offset":0,"length":11,"style":"fontfamily-Roboto, RobotoDraft, Helvetica, Arial, sans-serif"}],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"cb3b3e38-8b80-4a67-9cfa-94a75244e6ec","element":"NumberInput","required":false,"label":{"blocks":[{"key":"bonui","text":"INVOICE_AMT","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":0,"length":11,"style":"color-rgb(0,0,0)"},{"offset":0,"length":11,"style":"bgcolor-rgb(255,255,255)"},{"offset":0,"length":11,"style":"fontsize-13"},{"offset":0,"length":11,"style":"fontfamily-Roboto, RobotoDraft, Helvetica, Arial, sans-serif"}],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"0906ff80-aaab-4767-98a5-8e34fb7dc537","element":"TextArea","required":false,"label":{"blocks":[{"key":"6q2jf","text":"Installation Remarks","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":0,"length":20,"style":"color-rgb(0,0,0)"},{"offset":0,"length":20,"style":"bgcolor-rgb(255,255,255)"},{"offset":0,"length":20,"style":"fontsize-13"},{"offset":0,"length":20,"style":"fontfamily-Roboto, RobotoDraft, Helvetica, Arial, sans-serif"}],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"4a30eab4-9098-467d-a05a-bf5c10147f50","element":"NumberInput","required":false,"label":{"blocks":[{"key":"7lrq8","text":"Person Required","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":0,"length":15,"style":"color-rgb(0,0,0)"},{"offset":0,"length":15,"style":"bgcolor-rgb(255,255,255)"},{"offset":0,"length":15,"style":"fontsize-13"},{"offset":0,"length":15,"style":"fontfamily-Roboto, RobotoDraft, Helvetica, Arial, sans-serif"}],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"a376add9-d4b5-4557-9250-ed2d73024e66","element":"Files","label":{"blocks":[{"key":"8bu3m","text":"SOP","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"initialText":"","lambdaArn":""},{"id":"a6e74b1f-8f59-4609-84ce-3c12327135d6","element":"Files","label":{"blocks":[{"key":"4l261","text":"Drawer","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"initialText":"","lambdaArn":""}],"translatedFields":[{"key":"899384e6-31d1-4c6e-a7b6-9a666efb2a01","required":false,"label":"Size Category","widget":"select","options":[{"label":"Small","value":"adcc5084-7041-4b84-8c82-c363074420da"},{"label":"Medium","value":"64444a30-85a6-48c1-8c00-dc8ed30646a4"},{"label":"Large","value":"8bbcf7f9-1d81-4ad1-aa0e-6557fb4a3609"},{"label":"XL","value":"86c4de3d-3c77-4b30-9004-65f1d432c1cf"}]},{"key":"15f81209-b9e0-4d00-8bbe-fa728f92c24f","required":false,"label":"Products","widget":"select","options":[{"label":"Bedroom Accessories","value":"Value1"},{"label":"Bedside Tables","value":"Value2"},{"label":"Chest Of Drawers","value":"Value3"},{"label":"Chests","value":"Value4"},{"label":"Cupboards","value":"Value5"},{"label":"Dressers","value":"Value6"},{"label":"Mirrors","value":"Value7"},{"label":"Non-Storage Beds","value":"Value8"},{"label":"Single Beds","value":"Value9"},{"label":"Storage Beds","value":"Value10"},{"label":"Hydraulic Beds","value":"Value11"},{"label":"4 Seater Table","value":"Value12"},{"label":"6 Seater Table","value":"Value13"},{"label":"Dining Bench","value":"Value14"},{"label":"Dining Chairs","value":"Value15"},{"label":"Extendable Table","value":"Value16"},{"label":"Bar Stools","value":"Value17"},{"label":"Bar Units","value":"Value18"},{"label":"Dining Accessories","value":"Value19"},{"label":"Kitchen Racks","value":"Value20"},{"label":"Sideboards","value":"Value21"},{"label":"Bookshelf","value":"Value22"},{"label":"Coffee Tables","value":"Value23"},{"label":"Display Cabinets","value":"Value24"},{"label":"Entryway","value":"Value25"},{"label":"Laptop Tables","value":"Value27"},{"label":"Prayer Units","value":"Value28"},{"label":"Shoe Racks","value":"Value29"},{"label":"Side Tables","value":"Value30"},{"label":"Tv Unit","value":"Value31"},{"label":"Tv Units","value":"Value32"},{"label":"Wall Shelf","value":"Value33"},{"label":"Wardrobes","value":"Value34"},{"label":"Accent Chairs","value":"Value35"},{"label":"Benches","value":"Value36"},{"label":"Day Beds","value":"Value37"},{"label":"Lounge Chairs","value":"Value38"},{"label":"Loveseats","value":"Value39"},{"label":"Ottomans","value":"Value40"},{"label":"Recliners","value":"Value41"},{"label":"Sofa Beds","value":"Value42"},{"label":"Mattress","value":"Value43"},{"label":"Balcony Chairs","value":"Value44"},{"label":"Balcony Swings","value":"Value45"},{"label":"Balcony Tables","value":"Value46"},{"label":"Fabric Sofa","value":"Value47"},{"label":"Leather Sofa","value":"Value48"},{"label":"Leatherette Sofa","value":"Value49"},{"label":"Recliner Sofa","value":"Value50"},{"label":"Wooden Sofa","value":"Value51"},{"label":"Study Chairs","value":"Value52"},{"label":"Study Tables","value":"Value53"}],"widgetProps":{"mode":"multiple"}},{"key":"447e74f0-41c6-4c81-baf9-e9b3c0642b8f","required":false,"label":"Delivery Date","widget":"date-picker","widgetProps":{"style":{"width":"100%"}}},{"key":"fcd2b6c1-170b-40bd-bf4a-444f93ac8f85","required":false,"label":"ORDER_NO"},{"key":"960b442b-4305-48ca-a907-1c04c1e640af","required":false,"label":"Qty","widget":"number"},{"key":"4ea21a95-2739-4433-9610-9f5f47eec5d7","required":false,"label":"NO_OF_BOXES","widget":"number"},{"key":"cb3b3e38-8b80-4a67-9cfa-94a75244e6ec","required":false,"label":"INVOICE_AMT","widget":"number"},{"key":"0906ff80-aaab-4767-98a5-8e34fb7dc537","required":false,"label":"Installation Remarks","widget":"textarea"},{"key":"4a30eab4-9098-467d-a05a-bf5c10147f50","required":false,"label":"Person Required","widget":"number"},{"key":"a376add9-d4b5-4557-9250-ed2d73024e66","required":false,"label":"SOP","cust_component":"Files","cust_component_value":""},{"key":"a6e74b1f-8f59-4609-84ce-3c12327135d6","required":false,"label":"Drawer","cust_component":"Files","cust_component_value":""}]}',
    srvc_default_provider: 2,
    srvc_rate_fields_json:
        '{"originalFields":[{"id":"bc7f2fb8-8cf0-455b-b7a3-a834850590ab","element":"Rating","required":true,"label":{"blocks":[{"key":"a3iac","text":"How would you rate our service","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0,"numberOfStars":5},{"id":"5d25af9a-a32b-459c-9fe0-d715b6ea9005","element":"RadioButtons","required":true,"label":{"blocks":[{"key":"1g9ja","text":"Service was on time ?","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"6a4406b7-66b1-4729-b4c9-89faaaddbaf1","label":"Yes","value":"Yes","checked":false},{"id":"55e5bafc-73aa-4123-947d-80bb5018eb16","label":"No","value":"No","checked":false}]},{"id":"b6473217-6938-4f1d-b884-bd20b2b007e6","element":"Checkboxes","label":{"blocks":[{"key":"auuff","text":"Please check applicable","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"e3bb5173-a831-4d93-9ff8-6b8086131229","value":"Technician behaviour not good","checked":false},{"id":"11c24f6e-84a7-4fb5-b437-4d82d10a8159","value":"Technician had less information about the product","checked":false},{"id":"7a07d626-0aa3-4eb1-8afe-49e1a0de6f7c","value":"Technician argued a lot","checked":false}]},{"id":"5a5476f0-ae4f-43fd-a649-6d21723e8a2c","element":"TextArea","required":false,"label":{"blocks":[{"key":"5poob","text":"Feedback","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"e3f81213-29ad-4060-a447-a435da22a1b9","element":"Files","label":{"blocks":[{"key":"2ogat","text":"Add photos ( if any )","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false}],"translatedFields":[{"key":"bc7f2fb8-8cf0-455b-b7a3-a834850590ab","required":true,"label":"How would you rate our service","cust_widget":"Rating","widgetProps":{"count":5}},{"key":"5d25af9a-a32b-459c-9fe0-d715b6ea9005","required":true,"label":"Service was on time ?","widget":"radio-group","forwardRef":true,"options":[{"label":"Yes","value":"6a4406b7-66b1-4729-b4c9-89faaaddbaf1"},{"label":"No","value":"55e5bafc-73aa-4123-947d-80bb5018eb16"}]},{"key":"b6473217-6938-4f1d-b884-bd20b2b007e6","required":false,"label":"Please check applicable","widget":"checkbox-group","options":[{"label":"Technician behaviour not good","value":"e3bb5173-a831-4d93-9ff8-6b8086131229"},{"label":"Technician had less information about the product","value":"11c24f6e-84a7-4fb5-b437-4d82d10a8159"},{"label":"Technician argued a lot","value":"7a07d626-0aa3-4eb1-8afe-49e1a0de6f7c"}]},{"key":"5a5476f0-ae4f-43fd-a649-6d21723e8a2c","required":false,"label":"Feedback","widget":"textarea"},{"key":"e3f81213-29ad-4060-a447-a435da22a1b9","required":false,"label":"Add photos ( if any )","cust_component":"Files","cust_component_value":""}]}',
    srvc_enable_srvc_prvdr: true,
    srvc_type_billing_type: 'manday',
    deployment_who_can_edit: [3],
    srvc_type_icon_selector: 'icon-tickets',
    daily_update_form_fields:
        '{"originalFields":[{"id":"d1b74e9d-336a-42da-93a0-11f0fc4f9cf6","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"a6e1o","text":"Material issue","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"cacc9d9b-c2c5-4851-aa16-a49d93c60c46","label":"Yes","value":"Yes","checked":false},{"id":"91708f78-315d-45f5-9223-4a55f2b5ebb4","label":"No","value":"No","checked":false}]},{"id":"bd202080-5c5b-4484-b859-4e57d5bb4dd7","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"2cqng","text":"Site issue","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"cec04d2f-d34c-4d82-b44f-7c25509876af","label":"Yes","value":"Yes","checked":false},{"id":"cf412f80-2055-4c37-a95e-68013ea86f2b","label":"No","value":"No","checked":false}]},{"id":"234dd2f8-58bd-45c2-8d65-1aab6865afee","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"e346u","text":"Internal issue","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"32c7f58e-fec5-4435-9f44-553819d3cd4c","label":"Yes","value":"Yes","checked":false},{"id":"7cd57af4-ea42-4eeb-bcda-48893bbffc60","label":"No","value":"No","checked":false}]},{"id":"9f65569d-e931-4111-bfb5-b5b52d49a9f8","element":"TextInput","required":false,"label":{"blocks":[{"key":"5tsa4","text":"Material issue comments","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"47fe9598-965e-450f-a215-fabd46a8cdd4","element":"TextInput","required":false,"label":{"blocks":[{"key":"d2rsi","text":"Site issue comment","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"46371317-94ff-425c-a290-a1899a4c4dbe","element":"TextInput","required":false,"label":{"blocks":[{"key":"bk5aj","text":"Internal issue comments","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"66d157c8-7d0e-4a3f-a393-2c5e3e6d62c1","element":"Files","label":{"blocks":[{"key":"f54d7","text":"Material issue photos","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"d68c25ac-2e43-41e4-b2b4-82d259521e1d","element":"Files","label":{"blocks":[{"key":"bkqcb","text":"Site issue photos","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"eedad81d-49b3-4145-bfff-5ada498e7508","element":"Files","label":{"blocks":[{"key":"c95v5","text":"Internal issue photos","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false}],"translatedFields":[{"key":"d1b74e9d-336a-42da-93a0-11f0fc4f9cf6","required":false,"label":"Material issue","widget":"radio-group","forwardRef":true,"options":[{"label":"Yes","value":"cacc9d9b-c2c5-4851-aa16-a49d93c60c46"},{"label":"No","value":"91708f78-315d-45f5-9223-4a55f2b5ebb4"}]},{"key":"bd202080-5c5b-4484-b859-4e57d5bb4dd7","required":false,"label":"Site issue","widget":"radio-group","forwardRef":true,"options":[{"label":"Yes","value":"cec04d2f-d34c-4d82-b44f-7c25509876af"},{"label":"No","value":"cf412f80-2055-4c37-a95e-68013ea86f2b"}]},{"key":"234dd2f8-58bd-45c2-8d65-1aab6865afee","required":false,"label":"Internal issue","widget":"radio-group","forwardRef":true,"options":[{"label":"Yes","value":"32c7f58e-fec5-4435-9f44-553819d3cd4c"},{"label":"No","value":"7cd57af4-ea42-4eeb-bcda-48893bbffc60"}]},{"key":"9f65569d-e931-4111-bfb5-b5b52d49a9f8","required":false,"label":"Material issue comments"},{"key":"47fe9598-965e-450f-a215-fabd46a8cdd4","required":false,"label":"Site issue comment"},{"key":"46371317-94ff-425c-a290-a1899a4c4dbe","required":false,"label":"Internal issue comments"},{"key":"66d157c8-7d0e-4a3f-a393-2c5e3e6d62c1","required":false,"label":"Material issue photos","cust_component":"Files","cust_component_value":""},{"key":"d68c25ac-2e43-41e4-b2b4-82d259521e1d","required":false,"label":"Site issue photos","cust_component":"Files","cust_component_value":""},{"key":"eedad81d-49b3-4145-bfff-5ada498e7508","required":false,"label":"Internal issue photos","cust_component":"Files","cust_component_value":""}]}',
    srvc_type_enable_billing: true,
    daily_update_who_can_edit: [3],
    deployment_possible_roles: [3, 115, 117, 118],
    srvc_type_line_item_config:
        '{"4e58a651-7420-4cf3-ad27-f40926ff9a5c":{"key":"4e58a651-7420-4cf3-ad27-f40926ff9a5c","label":"kitchen","fields":"{\\"originalFields\\":[{\\"id\\":\\"02b0588c-e2cf-4f36-a956-3a374e23a02c\\",\\"element\\":\\"Dropdown\\",\\"label\\":{\\"blocks\\":[{\\"key\\":\\"4dv1c\\",\\"text\\":\\"Item\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"required\\":false,\\"options\\":[{\\"id\\":\\"62ca67ae-974d-45b2-8383-f6550fa25f32\\",\\"value\\":\\"Base cabinet\\"},{\\"id\\":\\"d82c50c1-dc54-45f2-b1bc-71f4f0a6b481\\",\\"value\\":\\"Loft\\"},{\\"id\\":\\"22425acf-f579-46fd-88b7-ea7242c10454\\",\\"value\\":\\"Wall cabinet\\"},{\\"id\\":\\"6a0d7b04-b922-4e5a-aeb1-a0bccaaf3149\\",\\"value\\":\\"Tall unit\\"},{\\"id\\":\\"78d49408-57af-4521-8a11-cae72e908ce9\\",\\"value\\":\\"Bar Unit\\"},{\\"id\\":\\"190dd10a-a4fe-4372-940a-c0eaf0aaf620\\",\\"value\\":\\"Chimney\\"},{\\"id\\":\\"e3b01744-8a7b-44bb-a037-48095baf2e20\\",\\"value\\":\\"Ledge\\"},{\\"id\\":\\"c62a829a-c975-444e-bdb6-4412c988f4d2\\",\\"value\\":\\"SM Base\\"},{\\"id\\":\\"e70d4710-648f-49ce-86d7-54ece5ecec4b\\",\\"value\\":\\"SM Wall\\"},{\\"id\\":\\"6d29233b-1f38-45c1-a9ff-085586a68a12\\",\\"value\\":\\"Steel Kitchen\\"}]},{\\"id\\":\\"eb48418e-c736-4b66-98ed-33b7c41c8f43\\",\\"element\\":\\"TextInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"eqs3q\\",\\"text\\":\\"Elevation\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":\\"\\"},{\\"id\\":\\"9263b1f4-2b5c-47a3-8f80-7eddd27fee2b\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"3h5c5\\",\\"text\\":\\"Width(mm)\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0},{\\"id\\":\\"9b5e64a1-ced8-4351-8672-3d4021b76f22\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"9pr23\\",\\"text\\":\\"Height(mm)\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0}],\\"translatedFields\\":[{\\"key\\":\\"02b0588c-e2cf-4f36-a956-3a374e23a02c\\",\\"required\\":false,\\"label\\":\\"Item\\",\\"widget\\":\\"select\\",\\"options\\":[{\\"label\\":\\"Base cabinet\\",\\"value\\":\\"62ca67ae-974d-45b2-8383-f6550fa25f32\\"},{\\"label\\":\\"Loft\\",\\"value\\":\\"d82c50c1-dc54-45f2-b1bc-71f4f0a6b481\\"},{\\"label\\":\\"Wall cabinet\\",\\"value\\":\\"22425acf-f579-46fd-88b7-ea7242c10454\\"},{\\"label\\":\\"Tall unit\\",\\"value\\":\\"6a0d7b04-b922-4e5a-aeb1-a0bccaaf3149\\"},{\\"label\\":\\"Bar Unit\\",\\"value\\":\\"78d49408-57af-4521-8a11-cae72e908ce9\\"},{\\"label\\":\\"Chimney\\",\\"value\\":\\"190dd10a-a4fe-4372-940a-c0eaf0aaf620\\"},{\\"label\\":\\"Ledge\\",\\"value\\":\\"e3b01744-8a7b-44bb-a037-48095baf2e20\\"},{\\"label\\":\\"SM Base\\",\\"value\\":\\"c62a829a-c975-444e-bdb6-4412c988f4d2\\"},{\\"label\\":\\"SM Wall\\",\\"value\\":\\"e70d4710-648f-49ce-86d7-54ece5ecec4b\\"},{\\"label\\":\\"Steel Kitchen\\",\\"value\\":\\"6d29233b-1f38-45c1-a9ff-085586a68a12\\"}]},{\\"key\\":\\"eb48418e-c736-4b66-98ed-33b7c41c8f43\\",\\"required\\":false,\\"label\\":\\"Elevation\\"},{\\"key\\":\\"9263b1f4-2b5c-47a3-8f80-7eddd27fee2b\\",\\"required\\":false,\\"label\\":\\"Width(mm)\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"9b5e64a1-ced8-4351-8672-3d4021b76f22\\",\\"required\\":false,\\"label\\":\\"Height(mm)\\",\\"widget\\":\\"number\\"}]}","name_field_label":"Kitchen item","name_field_formula":"{Item} - {Elevation}","quantity_field_label":"Sqft","quantity_field_formula":"({Width(mm)} * {Height(mm)}) / (304.8 * 304.8)","price_field_label":"Price"}}',
    srvc_is_cust_fields_dynamic: false,
    srvc_type_tabular_view_columns: [
        'srvc_prvdr',
        'request_priority',
        'request_description',
        'cust_full_name',
        'creation_date',
        'request_req_date',
        'full_address',
        'sbtsks',
        'attachments',
        'authority_3',
        'authority_115',
    ],
    '3_auto_assign_based_on_location': true,
    enable_lambda_for_status_update: false,
    srvc_feedback_ratings_field_key: 'bc7f2fb8-8cf0-455b-b7a3-a834850590ab',
    deployment_time_slot_lower_limit: '12:00AM',
    deployment_time_slot_upper_limit: '11:45PM',
    srvc_feedback_trigger_status_key: ['closed'],
    '115_auto_assign_based_on_location': false,
    restrict_manual_authority_selection: true,
    srvc_type_pricing_config_for_manday:
        '{"srvc_type_pricing_config_for_manday":{"32":300,"33":200,"input_table_id":"ebec0e4d-2d3e-4cd1-b749-1551f930f5fc","key":"ebec0e4d-2d3e-4cd1-b749-1551f930f5fc","manday_master_rate":100}}',
    srvc_type_who_can_sync_srvc_req_prc: [3],
    '02b0588c-e2cf-4f36-a956-3a374e23a02c':
        '62ca67ae-974d-45b2-8383-f6550fa25f32',
    '9263b1f4-2b5c-47a3-8f80-7eddd27fee2b': '',
    '9b5e64a1-ced8-4351-8672-3d4021b76f22': '',
    'eb48418e-c736-4b66-98ed-33b7c41c8f43': '',
    srvc_type_enable_billing_discounting: true,
    srvc_type_who_can_send_req_for_billing: [3],
    cnsmr_ntfctn_fr_status_open_sms_enabled: true,
    '3_enable_cross_visibility_of_authorities': true,
    cnsmr_ntfctn_fr_status_open_sms_template:
        'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
    '115_enable_cross_visibility_of_authorities': true,
    cnsmr_ntfctn_fr_status_open_sms_status_type: 'request_registration',
    srvc_type_who_can_lock_srvc_req_for_billing: [3],
    srvc_type_who_will_get_notified_for_billing: [3],
    '3_show_authorities_that_report_to_the_assigner': false,
    srvc_type_discount_approval_status_changes_notify: [3],
    srvc_type_manday_pricing_config_determination_engine: 'highest',
    'srvc_type_4e58a651-7420-4cf3-ad27-f40926ff9a5c_pricing_config_determination_engine':
        'highest',
    srvc_type_pricing_config_for_line_item: '{}',
    daily_update_track_line_item_progress: 'Yes',
    show_line_item_by_selection: true,
    selected_line_items: ['item1', 'item2'],
};

const reqData = {
    id: 388,
    title: 'HMLT240809949501',
    org_id: 3,
    status: {
        key: 'open',
        color: '#e91e63',
        title: 'Open',
        status_type: 'ACTIVE',
    },
    priority: 'Normal',
    assignees: [['73b1ba23-507b-4c7c-8465-b852d2a657e3']],
    form_data: {
        title: 'SMS sent to customer',
        host_d: 'localhost:3000',
        org_id: 3,
        usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        comment:
            'Dear Shambhu choudhary, Your request no:HMLT240809949501 has been registered with WIFY and will be attended shortly.',
        entry_id: 388,
        cust_city: 'Mumbai',
        mic_files: {},
        new_prvdr: '2',
        cust_state: 'MAHARASHTRA',
        ip_address: '::1',
        updatedDay: '2024-08-09',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        attachments: {
            'a376add9-d4b5-4557-9250-ed2d73024e66': [
                'org_3/1_2222316576708500_tRu5DuRJrPacFYgc.jpg',
            ],
        },
        authority_3: '842e25ae-b3bd-428a-8476-d01fd8458343',
        cust_mobile: '8108364951',
        camera_files: {},
        cust_pincode: '400068',
        srvc_type_id: '15',
        cust_full_name: 'Shambhu choudhary',
        request_priority: 'Normal',
        notification_data: true,
        is_customer_access: 0,
        update_for_comment: false,
        request_description: 'Ticket 2',
        srvc_type_his_db_id: '151',
        daily_status_updates: {
            '2024-08-09': {
                u_by: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                mic_files: {},
                attachments: {
                    '66d157c8-7d0e-4a3f-a393-2c5e3e6d62c1': [
                        'org_3/Shambhu%20Choudhary_2024-08-09_1_2220648660039500_aHv5DEhAcnwZwDZE.jpg',
                        'org_3/Selfie_19876659649043489_e7Z8XIDDluqQxnZK_2220604656935900_ga16HWmcB2Vxd7cY.jpeg',
                    ],
                },
                day_remarks: 'No remarks',
                day_progress: 0,
                update_day_and_time: 'Aug-09-2024 03:15 PM',
            },
        },
        prvdr_assg_timestamp: '2024-08-09T09:12:33.916391',
        sp_srvc_type_his_db_id: '63',
        geocoding_location_data: {
            location: {
                lat: 19.2590954,
                lng: 72.8338234,
            },
            location_type: 'APPROXIMATE',
        },
        lastChangedFileSectionIds: ['a376add9-d4b5-4557-9250-ed2d73024e66'],
    },
    is_deleted: false,
    all_ratings: [],
    sbtsk_types: [
        {
            icon: 'icon-map-directions',
            label: 'Visit',
            value: '2',
        },
        {
            icon: 'icon-feedback',
            label: 'Simple task',
            value: '3',
        },
    ],
    srvc_type_id: 15,
    revisions_data: [],
    sp_all_ratings: [],
    sp_config_data: [null],
    srvc_type_title: 'Tickets',
    location_grp_ids: [36, 32, 35],
    sp_revisions_data: [],
    subtaskConfigData: [
        {
            value: 2,
            statuses: [
                {
                    color: '#03a9f4',
                    title: 'Started Journey',
                    value: 'i6wd4zoT',
                },
                {
                    color: '#4caf50',
                    title: 'Job Complete',
                    value: 'AZu4zH7J',
                },
                {
                    color: '#9c27b0',
                    title: 'Work in Progress',
                    value: 'wc3GvRu7',
                },
                {
                    color: '#cddc39',
                    title: 'Scheduled',
                    value: 'open',
                },
                {
                    color: '#E1E1E1',
                    title: 'Closed',
                    value: 'closed',
                },
                {
                    color: '#ff9800',
                    title: 'Reached Site',
                    value: '5fFFPzc3',
                },
            ],
            config_data: {
                org_id: 3,
                usr_id: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                ip_address: '::1',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36',
                sbtsk_statuses:
                    '{"ACTIVE":[{"key":"open","color":"#cddc39","title":"Scheduled","status_type":"ACTIVE"},{"key":"i6wd4zoT","color":"#03a9f4","title":"Started Journey","status_type":"ACTIVE"},{"key":"5fFFPzc3","color":"#ff9800","title":"Reached Site","status_type":"ACTIVE"},{"key":"wc3GvRu7","color":"#9c27b0","title":"Work in Progress","status_type":"ACTIVE"}],"DONE":[{"key":"AZu4zH7J","color":"#4caf50","title":"Job Complete","status_type":"DONE"}],"CLOSED":[{"key":"closed","color":"#E1E1E1","title":"Closed","status_type":"CLOSED"}]}',
                sbtsk_type_key: 'visit',
                sbtsk_type_desc: 'On field visit task',
                sbtsk_type_name: 'Visit',
                sbtsk_can_reject: true,
                sbtsk_is_onfield: true,
                sbtsk_can_postpone: true,
                sbtsk_reject_fields:
                    '{"originalFields":[{"id":"e8b612aa-e344-47e2-a265-284733461a6c","element":"Dropdown","label":{"blocks":[{"key":"3ugq1","text":"Rejection reason","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":true,"options":[{"id":"4c9c3c73-9e2c-4f53-96e8-4182d43ed1e8","value":"Working on another site"},{"id":"e49a56e7-5c9a-42f4-936e-569aa63a9d9f","value":"On leave"}]}],"translatedFields":[{"key":"e8b612aa-e344-47e2-a265-284733461a6c","required":true,"label":"Rejection reason","widget":"select","options":[{"label":"Working on another site","value":"4c9c3c73-9e2c-4f53-96e8-4182d43ed1e8"},{"label":"On leave","value":"e49a56e7-5c9a-42f4-936e-569aa63a9d9f"}]}]}',
                sbtsk_postpone_fields:
                    '{"originalFields":[{"id":"3461fb01-491b-4b95-ab6b-fce0d6aa5f8d","element":"Dropdown","label":{"blocks":[{"key":"21lf2","text":"Reason for postpone","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":true,"options":[{"id":"f4b213b6-6421-4cb8-8995-1799b65ab5b1","value":"Customer asked for it"},{"id":"9fd2d223-b941-4163-81ce-c962d0fdbb59","value":"Customer not available"},{"id":"35f1fbd3-d6d2-43a7-8d17-3730f42a868e","value":"No entry provided at premise"}]}],"translatedFields":[{"key":"3461fb01-491b-4b95-ab6b-fce0d6aa5f8d","required":true,"label":"Reason for postpone","widget":"select","options":[{"label":"Customer asked for it","value":"f4b213b6-6421-4cb8-8995-1799b65ab5b1"},{"label":"Customer not available","value":"9fd2d223-b941-4163-81ce-c962d0fdbb59"},{"label":"No entry provided at premise","value":"35f1fbd3-d6d2-43a7-8d17-3730f42a868e"}]}]}',
                sbtsk_type_icon_selector: 'icon-map-directions',
                sbtsk_status_5fFFPzc3_fields:
                    '{"originalFields":[{"id":"91ebfb73-d164-41cd-bdcc-94689f89029c","element":"Files","label":{"blocks":[{"key":"85e5g","text":"Selfie at site","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":true}],"translatedFields":[{"key":"91ebfb73-d164-41cd-bdcc-94689f89029c","required":true,"label":"Selfie at site","cust_component":"Files","cust_component_value":""}]}',
                sbtsk_status_i6wd4zoT_fields:
                    '{"originalFields":[{"id":"49dbae78-0bb4-46eb-a786-c951c707143d","element":"RadioButtons","required":true,"label":{"blocks":[{"key":"e3boo","text":"Starting late ?","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"6d738f05-f89d-425f-b8b4-a6d2034cfb6d","label":"Yes","value":"yes","checked":false},{"id":"787996c7-7af6-42fd-8877-a07dac5aab41","label":"No","value":"no","checked":false}]}],"translatedFields":[{"key":"49dbae78-0bb4-46eb-a786-c951c707143d","required":true,"label":"Starting late ?","widget":"radio-group","forwardRef":true,"options":[{"label":"Yes","value":"6d738f05-f89d-425f-b8b4-a6d2034cfb6d"},{"label":"No","value":"787996c7-7af6-42fd-8877-a07dac5aab41"}]}]}',
            },
        },
    ],
    status_transitions: [
        {
            id: 435,
            key: 'open',
            c_by: 'Shambhu Choudhary',
            time: '2024-08-09T09:12:33.916391',
            u_by: null,
            c_meta: {
                time: '2024-08-09T09:12:33.916391',
                ip_addr: '::1',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            },
            u_meta: null,
            details: {
                color: '#e91e63',
                title: 'Open',
                status: 'open',
            },
        },
    ],
    billing_final_amount: 0,
    download_line_item_data: null,
    srvc_req_locked_by_name: null,
    send_for_billing_by_name: null,
    srvc_req_contact_numbers: [
        {
            key: 'cust_mobile',
            label: 'Primary Number',
            value: '8108364951',
        },
    ],
    discount_approved_by_name: null,
    allow_to_see_line_item_tab: true,
    download_sp_line_item_data: null,
    sp_srvc_req_locked_by_name: null,
    sp_send_for_billing_by_name: null,
    allow_sp_line_items_download: true,
    sp_discount_approved_by_name: null,
    allow_site_attendance_download: true,
    allow_sp_daily_updates_download: true,
    srvc_type_history_pricing_config: [
        {
            srvc_type_pricing_config_for_manday:
                '{"srvc_type_pricing_config_for_manday":{"32":300,"33":200,"input_table_id":"ebec0e4d-2d3e-4cd1-b749-1551f930f5fc","key":"ebec0e4d-2d3e-4cd1-b749-1551f930f5fc","manday_master_rate":100}}',
            srvc_type_pricing_config_for_line_item: null,
        },
    ],
    sp_srvc_type_history_pricing_config: [
        {
            srvc_type_pricing_config_for_manday: null,
            srvc_type_pricing_config_for_line_item: null,
        },
    ],
    is_enable_auto_assign_authorities_refresh_btn: true,
};

const urlToSubmitFrUpdates = '/services/modify/15/388';

const isSrvcPrvdrTab = false;

describe(`${prefix} onFilesChanged`, () => {
    let section;
    let files;
    let filesBySection;
    let setFilesBySection;
    let onFormValueChange;
    let deletedFileUrl;

    beforeEach(() => {
        section = 'section1';
        files = ['file1', 'file2'];
        filesBySection = { section1: ['file1'] };
        setFilesBySection = jest.fn();
        onFormValueChange = jest.fn();
        deletedFileUrl = undefined;
    });

    test(`${prefix} smoke test`, async () => {
        await waitFor(() =>
            render(
                <DailyReqStatusEditor
                    editorDayDetails={editorDayDetails}
                    srvcConfigData={srvcConfigData}
                    reqData={reqData}
                    urlToSubmitFrUpdates={urlToSubmitFrUpdates}
                    isSrvcPrvdrTab={isSrvcPrvdrTab}
                />
            )
        );
    });

    test(`${prefix} should update filesBySection with new files when new files are added`, () => {
        const expectedFilesBySection = {
            section1: ['file1', 'file2'],
        };

        onFilesChangedFn(
            section,
            files,
            filesBySection,
            setFilesBySection,
            onFormValueChange
        );

        expect(setFilesBySection).toHaveBeenCalledWith(expectedFilesBySection);
        expect(onFormValueChange).toHaveBeenCalled();
    });

    test(`${prefix} should combine existing files with new files and ensure uniqueness`, () => {
        files = ['file1', 'file3'];
        const expectedFilesBySection = {
            section1: ['file1', 'file3'],
        };

        onFilesChangedFn(
            section,
            files,
            filesBySection,
            setFilesBySection,
            onFormValueChange
        );

        expect(setFilesBySection).toHaveBeenCalledWith(expectedFilesBySection);
        expect(onFormValueChange).toHaveBeenCalled();
    });

    test(`${prefix} should update filesBySection with combined files if no existing files in section`, () => {
        filesBySection = {};
        const expectedFilesBySection = {
            section1: ['file1', 'file2'],
        };

        onFilesChangedFn(
            section,
            files,
            filesBySection,
            setFilesBySection,
            onFormValueChange
        );

        expect(setFilesBySection).toHaveBeenCalledWith(expectedFilesBySection);
        expect(onFormValueChange).toHaveBeenCalled();
    });

    test(`${prefix} should remove a file from the section if deletedFileUrl is provided`, () => {
        files = ['file1', 'file2', 'file3'];
        deletedFileUrl = 'file2';
        const expectedFilesBySection = {
            section1: ['file1', 'file3'],
        };

        onFilesChangedFn(
            section,
            files,
            filesBySection,
            setFilesBySection,
            onFormValueChange,
            deletedFileUrl
        );

        expect(setFilesBySection).toHaveBeenCalledWith(expectedFilesBySection);
        expect(onFormValueChange).toHaveBeenCalled();
    });

    test(`${prefix} should handle the case when filesBySection[section] is undefined`, () => {
        filesBySection = { section2: ['fileA'] };
        const expectedFilesBySection = {
            section2: ['fileA'],
            section1: ['file1', 'file2'],
        };

        onFilesChangedFn(
            section,
            files,
            filesBySection,
            setFilesBySection,
            onFormValueChange
        );

        expect(setFilesBySection).toHaveBeenCalledWith(expectedFilesBySection);
        expect(onFormValueChange).toHaveBeenCalled();
    });

    test(` ${prefix} should display line item progress select when showLineItemBySelection is true`, async () => {
        // Render the component with required props
        ConfigHelpers.doesUserHaveOneOfTheRole = jest
            .fn()
            .mockReturnValue(true);
        render(
            <DailyReqStatusEditor
                showEditor={true}
                reqData={reqData}
                day="2023-10-01"
                projectId="project123"
                canCurrentUserEdit={true}
                showLineItemBySelection={true}
                trackLineItemWiseProgress={true}
                editorDayDetails={editorDayDetails}
                srvcConfigData={srvcConfigData}
            />
        );

        // Wait for the component to render and check if the line item select is displayed
        await waitFor(() => {
            const lineItemSelect = screen.getByText(/Line Item Progress/i);
            expect(lineItemSelect).toBeInTheDocument();
        });
    });
    test(`${prefix} should display static line item labels with sliders when items are selected`, async () => {
        // Mock ConfigHelpers to allow editing
        ConfigHelpers.doesUserHaveOneOfTheRole = jest
            .fn()
            .mockReturnValue(true);

        // Render the component with required props
        render(
            <DailyReqStatusEditor
                showEditor={true}
                reqData={reqData}
                day="2023-10-01"
                projectId="project123"
                canCurrentUserEdit={true}
                showLineItemBySelection={true}
                trackLineItemWiseProgress={true}
                editorDayDetails={editorDayDetails}
                srvcConfigData={{
                    ...srvcConfigData,
                    srvc_type_line_item_config: JSON.stringify({
                        group1: {
                            label: 'Kitchen Items',
                            fields: '[]',
                            name_field_formula: '{Type}',
                            items: [
                                {
                                    input_table_id: 'item1',
                                    Type: 'Cabinet',
                                    label: 'Cabinet',
                                },
                                {
                                    input_table_id: 'item2',
                                    Type: 'Countertop',
                                    label: 'Countertop ',
                                },
                            ],
                        },
                    }),
                }}
            />
        );

        // Wait for the component to render
        await waitFor(() => {
            // Check if the line item select is displayed
            const lineItemSelect = screen.getByText(/Line Item Progress/i);
            expect(lineItemSelect).toBeInTheDocument();

            // Check that sliders are present for each item
            const sliders = screen.getAllByRole('slider');
            expect(sliders.length).toBeGreaterThanOrEqual(1);
        });
    });
});
