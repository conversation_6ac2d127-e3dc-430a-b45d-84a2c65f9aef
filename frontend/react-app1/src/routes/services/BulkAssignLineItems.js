import { Form } from 'antd';
import FormBuilder from 'antd-form-builder';
import React, { useEffect, useRef, useState } from 'react';
import BulkUploader from '../../components/wify-utils/BulkUploader';
const baseSubmitUrl = '/services/modify';

const BulkAssignLineItems = (props) => {
    const [form] = Form.useForm();
    const forceUpdate = FormBuilder.useForceUpdate();
    const [selectedLineItemGroup, setSelectedLineItemGroup] = useState();
    const submitUrl = useRef(baseSubmitUrl);

    const initApiUrls = (props, value) => {
        const is_bulk_update = true;
        const srvcId = props.srvcDetails?.srvc_id;
        const isCustAccess = props.isCustAcces ? '1' : '0';

        submitUrl.current = `${baseSubmitUrl}/${value}/${srvcId}/${isCustAccess}/${is_bulk_update ? '1' : '0'}`;
    };

    const getSrvcTypeLineItemConfig = () => {
        const srvcTypeConfig = props?.srvcConfigData;
        const srvcTypeLineItemConfig = JSON.parse(
            srvcTypeConfig?.srvc_type_line_item_config || '{}'
        );

        return srvcTypeLineItemConfig;
    };

    //use antd formBuilder
    const getMeta = () => {
        const options = [];
        const srvcTypeConfig = props?.srvcConfigData;
        const srvcTypeLineItemConfig = getSrvcTypeLineItemConfig();

        Object.entries(srvcTypeLineItemConfig).forEach(
            ([sectionKey, sectionObj]) => {
                options.push({
                    key: sectionKey,
                    value: sectionKey,
                    label: sectionObj.label,
                });
            }
        );

        return {
            formItemLayout: null,
            fields: [
                {
                    key: 'select_line_item_group',
                    label: 'Select line item group',
                    widget: 'select',
                    options: options,
                    onChange: (value) => {
                        setSelectedLineItemGroup(value);
                        initApiUrls(props, value);
                        forceUpdate();
                    },
                    colSpan: 4,
                },
            ],
        };
    };

    const getColMetaFrBulkAssign = () => {
        const srvcTypeLineItemConfig = getSrvcTypeLineItemConfig();
        const selectedLineItemGroupConfig =
            srvcTypeLineItemConfig[selectedLineItemGroup];
        const quantityCol = selectedLineItemGroupConfig?.quantity_field_formula
            ? []
            : [
                  {
                      key: 'qty',
                      label: 'Quantity',
                      widget: 'number',
                      colSpan: 4,
                  },
              ];
        const priceCol = selectedLineItemGroupConfig?.price_field_label
            ? [
                  {
                      key: 'rate',
                      label: `${selectedLineItemGroupConfig?.price_field_label}`,
                      widget: 'number',
                      colSpan: 4,
                  },
              ]
            : [
                  {
                      key: 'rate',
                      label: 'Price',
                      widget: 'number',
                      colSpan: 4,
                  },
              ];

        const fields = selectedLineItemGroupConfig?.fields;

        if (fields) {
            const parsedFields = JSON.parse(fields);
            const translatedFields = parsedFields.translatedFields || [];

            return [
                {
                    key: 'tms_display_code',
                    label: 'Ticket ID',
                    required: true,
                    colSpan: 4,
                },
                ...translatedFields,
                ...quantityCol,
                ...priceCol,
            ];
        }
        return [];
    };

    return (
        <Form className="gx-w-100" layout="vertical" form={form}>
            <FormBuilder form={form} meta={getMeta()} />
            {selectedLineItemGroup && getColMetaFrBulkAssign()?.length > 0 && (
                <BulkUploader
                    key={selectedLineItemGroup}
                    // demoMode
                    // renderFormsForRows
                    // debugMode
                    onDataModified={(entry_ids) => {
                        if (props.tellParentToRefreshList) {
                            props.tellParentToRefreshList(0);
                        }
                    }}
                    errorHasLineBreaks={true}
                    submitUrl={submitUrl.current}
                    dataProto={getColMetaFrBulkAssign()}
                    timeFormatMsg
                    // isBulkAssignComp
                />
            )}
        </Form>
    );
};

export default BulkAssignLineItems;
