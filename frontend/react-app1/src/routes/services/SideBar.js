import React, { Component } from 'react';
import { Button } from 'antd';
import CustomScrollbars from '../../util/CustomScrollbars';
// import { filters } from "./data/data";
import QuickFilters from '../../components/wify-utils/crud/overview/QuickFilters';
import ConfigHelpers from '../../util/ConfigHelpers';

export class SideBar extends Component {
    state = {
        activeFilters: this.props.activeFilters,
        serviceTypeDetails: this.props.srvcDetails,
        showBulkCreateButton: ConfigHelpers.showBulkCreateButton(),
    };

    handleFilterChange = (newFilterObject) => {
        this.setState(
            {
                activeFilters: {
                    // ...this.state.activeFilters,
                    ...newFilterObject,
                },
            },
            () => this.props.onFilterChange(this.state.activeFilters)
        );
    };

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.activeFilters != this.props.activeFilters) {
            this.setState({
                activeFilters: this.props.activeFilters,
            });
        }
        // this.filters = this.props.filters;
    }

    render() {
        const { activeFilters, serviceTypeDetails, showBulkCreateButton } =
            this.state;
        const filters = this.props.filters;
        let srvc_icon,
            srvc_title,
            userRights = [];
        if (this.props.isCustomerRequests) {
            srvc_icon = 'icon-breadcrumb';
            srvc_title = 'Customer requests';
        } else {
            userRights = ConfigHelpers?.getUserRights(
                serviceTypeDetails?.srvc_id
            );
            srvc_icon = { ...serviceTypeDetails }.srvc_icon;
            srvc_title = { ...serviceTypeDetails }.srvc_title;
        }
        const isCustAcces = this.props.isCustAcces;
        return (
            <div className="gx-module-side">
                <div className="gx-module-side-header">
                    <div className="gx-module-logo">
                        <i className={`icon ${srvc_icon} gx-mr-2`} />
                        <span>{srvc_title}</span>
                    </div>
                </div>

                <div className="gx-module-side-content">
                    <CustomScrollbars className="gx-module-side-scroll">
                        {userRights?.includes('CREATE') || isCustAcces ? (
                            <div className="gx-module-add-task gx-pb-2">
                                <Button
                                    className="gx-btn-block ant-btn"
                                    type="primary"
                                    aria-label="add"
                                    onClick={this.props.onAddClick}
                                >
                                    <i className="icon icon-add-circle gx-mr-2" />
                                    <span>Add </span>
                                </Button>
                            </div>
                        ) : (
                            <div className="gx-my-3"></div>
                        )}
                        {(userRights?.includes('UPDATE') ||
                            (isCustAcces &&
                                !this.props?.isSrvcTypeNatureProject)) && (
                            <div className="gx-module-add-task gx-py-0 gx-pb-2">
                                <Button
                                    className="gx-btn-block ant-btn gx-btn-outline-primary"
                                    type="dashed"
                                    onClick={this.props.onAddBulkAssign}
                                >
                                    <i className="icon icon-signup gx-mr-2" />
                                    <span>Bulk assign</span>
                                </Button>
                            </div>
                        )}
                        {showBulkCreateButton &&
                            this.props?.isCustomerRequests && (
                                <div className="gx-module-add-task gx-py-0 gx-pb-2">
                                    <Button
                                        className="gx-btn-block ant-btn gx-btn-primary"
                                        type="dashed"
                                        onClick={this.props.onBulkCreate}
                                    >
                                        <i className="icon icon-signup gx-mr-2" />
                                        <span>Bulk create</span>
                                    </Button>
                                </div>
                            )}
                        <QuickFilters
                            filters={filters}
                            onFilterChange={this.handleFilterChange}
                            linkMode
                            activeFilters={activeFilters}
                            showSorting
                            staticFilters={this.props.staticFilters}
                        />
                    </CustomScrollbars>
                </div>
            </div>
        );
    }
}
