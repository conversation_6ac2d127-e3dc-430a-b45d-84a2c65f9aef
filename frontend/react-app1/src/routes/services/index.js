import {
    <PERSON><PERSON>,
    Col,
    Drawer,
    List,
    Row,
    Tooltip,
    Menu,
    message,
    Popconfirm,
    Spin,
    Typography,
    Dropdown,
    Space,
    DatePicker,
    Modal,
    Skeleton,
} from 'antd';
import { Avatar } from 'antd';
import React, { Component } from 'react';
import AppModuleHeader from '../../components/AppModuleHeader';
import CircularProgress from '../../components/CircularProgress';
import ConfigHelpers from '../../util/ConfigHelpers';
import CustomScrollbars from '../../util/CustomScrollbars';
import PagedApiListViewV2 from '../../components/wify-utils/crud/overview/PagedApiListViewV2';
import {
    getAnyObjectFrFilter,
    getColorInBetweenByPercentage,
    getCustomFileFieldsFilter,
    getIsDeletedFrFilter,
    getNoOfTasksObjectFrFilter,
    getOptionsListFrPossibleNumbers,
    getPriorityObjectFrFilter,
    getValueDataFrmFormMeta,
    handleFilterClearDateIfNull,
    PROJECT_BASED_SERVICE_TYPE,
    getEmptyObjectFrFilter,
    getPresetRangesForRangeDatePicker,
    setHtmlHeadTitle,
} from '../../util/helpers';
import http_utils from '../../util/http_utils';
import { SideBar } from './SideBar';
import ItemEditor from './ItemEditor';
import { filters, filtersFrPrvdr } from './filters';
import {
    convertUTCToDisplayTime,
    getTextColorFrPriority,
} from '../../util/helpers';
import Widget from '../../components/Widget/index';
import StatusChanger from './StatusChanger';
import {
    DownloadOutlined,
    DownOutlined,
    FilterFilled,
    LinkOutlined,
} from '@ant-design/icons';
import { getFilterFormMetaFilledWithApiData } from '../../components/wify-utils/crud/overview/filter_helpers';
import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';
import { isArray } from 'lodash';
import ExporterModal from './ExporterModal';
import BulkAssign from './BulkAssign';
import { Link } from 'react-router-dom';
import DeleteSrvcReqButton from './DeleteSrvcRequest';
import {
    addDefaultKeysForTabularView,
    formatAddressWithoutPincode,
    formatTimeRange,
    getAddressInfoMeta,
    getAuthorityLabelValuePairsWithPrefix,
    getAvgGaiRatingOfSubTasks,
    getCustomerInfoMeta,
    getDefaultColumnsForTabularView,
    getIsTabularView,
    getList,
    getRequestInfoMeta,
    showExtOrderId,
} from './helpers';
import _ from 'lodash';
import BulkCreate from './BulkCreate';
import moment from 'moment';
import { LuClock } from 'react-icons/lu';
import GaiWrapper from '../../components/wify-utils/Gai/GaiWrapper';
import DroppableWrapper from '../quick-assignment/ServiceReqsData/DroppableWrapper';
import UserName from '../../components/wify-utils/UserName';
import SkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader';
import CustomerRequestTicketCardView from '../../components/WIFY/WifyComponents/SkeletonLoader/cardView/ticketLoader/CustomerRequestTicketCardView';
import CustomerRequestTicketTableView from '../../components/WIFY/WifyComponents/SkeletonLoader/tableView/ticketLoader/CustomerRequestTicketTableView';
import PaginationLoader from '../../components/WIFY/WifyComponents/SkeletonLoader/pagination';
import MiniSidebarSkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader/miniSidebar';
import StarRatingCompact from '../../components/WIFY/WifyComponents/StarRatingCompact';

const { Paragraph, Text } = Typography;
const protoUrl = '/services/overview_proto';
const dataUrl = '/services/list';
const deleteUrl = '/services/modify';
class ServiceOverview extends Component {
    state = {
        activeFilters: this.getFiltersFrmSearch(),
        searchFilter: this.getSearchFromUrl(),
        drawerState: false,
        showEditor: false,
        showExporter: false,
        showItemEditor: false,
        isLoadingViewData: true,
        viewData: null,
        error: '',
        editModeForceRefreshDone: false,
        srvc_id: this.isCustomerRequests()
            ? '0'
            : this.props.match.params.srvc_id ||
              this.props.match.params.cust_srvc_id,
        editorItemConfig: undefined,
        isCustAcces: this.props.isCustAcces,
        showBulkAssignEditor: false,
        render_helper: false,
        srvc_type_id_vs_config: [],
        autoOpenItemEditor: false,
        showBulkCreator: false,
        ellipsis: true,
        pagedApi: {
            dataLoading: true,
            paginationLoading: true,
        },
    };
    handleLoadingChangeFromPagedApi = ({ dataLoadingStatus }) => {
        this.setState((prevState) => ({
            pagedApi: {
                ...prevState.pagedApi,
                dataLoading: dataLoadingStatus,
            },
        }));
    };

    getVisiblityOfItemEditor() {
        let autoOpenItemEditorVisibility = new URLSearchParams(
            window.location.search
        ).get('showItemEditor');
        return autoOpenItemEditorVisibility
            ? autoOpenItemEditorVisibility
            : false;
    }
    getSearchFromUrl() {
        let searchFrmLink = new URLSearchParams(window.location.search).get(
            'query'
        );
        return searchFrmLink ? searchFrmLink : '';
    }
    getFiltersFrmSearch() {
        let parsedFilter = new URLSearchParams(window.location.search).get(
            'filters'
        );
        return parsedFilter ? JSON.parse(parsedFilter) : {};
    }

    constructor(props) {
        super(props);
        this.userRights = ConfigHelpers.getUserRights(this.state.srvc_id);
        this.serviceTypeDetails = ConfigHelpers.getSrvcRoute(
            this.state.srvc_id,
            this.state.isCustAcces,
            this.props.config_data
        );
    }

    componentDidMount() {
        this.initViewData();
    }

    //The function named isCustomerRequests checks both if quick assignment is available and if there are customer requests.
    isCustomerRequests() {
        return (
            this.isQuickAssign() ||
            this.props.match?.path == '/customer-requests'
        );
    }

    isQuickAssign() {
        return this.props.forQuickAssign;
    }

    initViewData(forFilterRefresh = false) {
        if (forFilterRefresh || this.state.viewData == null) {
            this.setState({
                isLoadingViewData: forFilterRefresh ? false : true,
            });
            handleFilterClearDateIfNull(this.state.activeFilters);
            var params = {
                filters: JSON.stringify(this.state.activeFilters || {}), //Show vertical based custom fields in dump download (customer requests)
            };
            const onComplete = (resp) => {
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
                // Parse the selected vertical filter from URL
                const filterParams = this.getFiltersFrmSearch();
                const verticalId = filterParams?.verticals_list?.[0];
                if (verticalId) {
                    const selectedVertical =
                        resp.data.filters_proto.verticals_list.find(
                            (item) => item.value == verticalId
                        );

                    if (selectedVertical) {
                        // Update the header title dynamically
                        setHtmlHeadTitle(
                            this.props.location.pathname,
                            selectedVertical.label
                        );
                    }
                }
            };
            const onError = (error) => {
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            http_utils.performGetCall(
                protoUrl +
                    '/' +
                    this.state.srvc_id +
                    '/' +
                    (this.state.isCustAcces ? '1' : '0'),
                params,
                onComplete,
                onError
            );
        }
    }

    resetFilter = () => {
        this.setState(
            {
                activeFilters: {},
            },
            () => this.initViewData(true)
        );
    };

    handleFilterChange = (newFilterObject) => {
        this.setState({
            activeFilters: {
                // ...this.state.activeFilters,
                ...newFilterObject,
            },
        });
    };

    handleSearchChange = (query) => {
        this.setState({
            searchFilter: query,
        });
    };

    onToggleDrawer() {
        this.setState({
            drawerState: !this.state.drawerState,
        });
    }

    onAddItem = () => {
        this.setState({ showEditor: true });
    };

    onAddBulkAssign = () => {
        this.setState({ showBulkAssignEditor: true });
    };

    onBulkCreate = () => {
        this.setState({ showBulkCreator: true });
    };

    onSingleRowClick = (item, type_id_vs_config) => {
        this.setState({
            editorItem: item,
            showItemEditor: true,
            editorItemConfig: this.getMatchingConfigFrmItem(
                type_id_vs_config,
                item
            ),
        });
    };

    tableOnRowClick = (item) => {
        this.setState({
            editorItem: item,
            showItemEditor: true,
            editorItemConfig: this.getMatchingConfigFrmItem(
                this.getSrvcTypeIdVsConfig(),
                item
            ),
        });
    };

    getExportMenuData() {
        const isCustomerRequests = this.isCustomerRequests();
        const isCustomerAccess = this.props.isCustAcces;

        // Check if export is restricted based on section
        const isExportRestricted =
            ConfigHelpers.restrictDumpDownloadsForSection(
                isCustomerAccess ? 'customer_access' : 'customer_requests'
            );

        // Render empty menu if export is restricted
        if (isExportRestricted) {
            return null;
        }

        return (
            <Menu>
                <Menu.Item
                    key="export_request"
                    onClick={(e) => this.onExportClick()}
                >
                    <span>
                        <DownloadOutlined /> Export request
                    </span>
                </Menu.Item>
            </Menu>
        );
    }

    onExportClick() {
        // message.success('Initiating export');
        this.setState({ showExporter: true });
    }

    getMatchingConfigFrmItem(type_id_vs_config, item) {
        let matchingConfig = type_id_vs_config.filter(
            (singleTypeData) => item.srvc_type_id == singleTypeData.srvc_id
        );
        matchingConfig =
            matchingConfig.length > 0 ? matchingConfig[0] : undefined;
        return matchingConfig;
    }

    getIsGaiRatingFilterEnabled = () => {
        let returnFieldsMeta = [];
        returnFieldsMeta.push({
            key: 'is_gai_rated_fr_sbtsk',
            label: 'GAI rated(Only one)',
            widget: 'select',
            quick: true,
            widgetProps: {
                mode: 'multiple',
                optionFilterProp: 'children',
            },
            options: [
                {
                    label: 'Yes',
                    value: true,
                    icon: 'icon-check-cricle gx-text-green ',
                },
                {
                    label: 'No',
                    value: false,
                    icon: 'icon-check-circle-o gx-text-grey',
                },
            ],
        });
        return returnFieldsMeta;
    };
    getSbtskGaiRatingFieldMetaFrFilter = () => {
        let returnFieldsMeta = [];
        let numberOfRatingValue = 5;
        let possibleRatingOptions = [];
        possibleRatingOptions = getOptionsListFrPossibleNumbers(
            numberOfRatingValue,
            (number) => ({
                label: (
                    <div>
                        {number}
                        <img
                            src="https://static.wify.co.in/images/website/tms/star.png"
                            className="wy-rating-img-small"
                        />
                    </div>
                ),
                value: number,
            })
        ).filter((item) => item.value != 0);

        returnFieldsMeta.push({
            key: 'sbtsk_gai_rating',
            label: 'Subtask GAI Rating',
            placeholder: 'Select..',
            widget: 'select',
            quick: true,
            widgetProps: {
                // make sure to add mode as multiple when its for quick
                mode: 'multiple',
                optionFilterProp: 'children',
                showSearch: true,
            },
            options: possibleRatingOptions,
        });
        return returnFieldsMeta;
    };
    getFeedbackFilters = () => {
        let returnFieldsMeta = [];
        let configData = this.state.viewData?.config_data;
        returnFieldsMeta.push({
            key: 'feedback_received',
            label: 'Feedback Received',
            widget: 'select',
            quick: true,
            widgetProps: {
                mode: 'multiple',
                optionFilterProp: 'children',
            },
            options: [
                {
                    label: 'Yes',
                    value: true,
                    icon: 'icon-check-cricle gx-text-green ',
                },
                {
                    label: 'No',
                    value: false,
                    icon: 'icon-check-circle-o gx-text-grey',
                },
            ],
            noAny: true,
        });
        if (configData) {
            let rateFieldKey = configData.srvc_feedback_ratings_field_key;
            if (rateFieldKey) {
                let fullFeedbackFormMeta = decodeFieldsMetaFrmJson(
                    configData.srvc_rate_fields_json
                );
                let rateFieldMeta = fullFeedbackFormMeta.filter(
                    (fieldMeta) => fieldMeta.key == rateFieldKey
                )[0];
                let numberOfStars = rateFieldMeta.widgetProps.count;
                // console.log("numberOfStars",numberOfStars);
                let possibleStarsOptions = getOptionsListFrPossibleNumbers(
                    numberOfStars,
                    (number) => ({
                        color: getColorInBetweenByPercentage(
                            [0, 150, 136], // Min color
                            [233, 30, 99], // Max color
                            number / numberOfStars
                        ),
                    })
                ).reverse();
                // console.log('possibleStarsOptions',possibleStarsOptions)
                returnFieldsMeta.push({
                    key: 'feedback_star',
                    label: 'Feedback Star',
                    widget: 'select',
                    quick: true,
                    widgetProps: {
                        mode: 'multiple',
                        optionFilterProp: 'children',
                    },
                    options: possibleStarsOptions,
                });
            }
        }
        return returnFieldsMeta;
    };

    getPrvdrByKey(currentSrvcPrvdrKey) {
        return this.state.viewData?.all_srvc_prvdrs?.filter(
            (singlePrvdr) => singlePrvdr.value == currentSrvcPrvdrKey
        )[0];
    }

    getOrgLabelCmpnt(item) {
        return (
            this.isCustomerRequests() && (
                <p className="gx-mt-0 gx-mb-1">
                    <div className="gx-card gx-border gx-border-blue gx-px-2 gx-py-1 gx-d-flex">
                        <Avatar
                            className={`gx-size-20 gx-mr-2 gx-vertical-align-bottom `}
                            src={item.org.icon_path}
                        ></Avatar>
                        {item.org.label}
                    </div>
                </p>
            )
        );
    }

    getSrvcDetails() {
        if (this.isCustomerRequests()) {
            return this.state.editorItemConfig;
        } else {
            return this.serviceTypeDetails;
        }
    }
    getSrvcConfigData() {
        if (this.isCustomerRequests()) {
            return this.state.editorItemConfig?.srvc_config;
        } else {
            return this.state.viewData?.config_data;
        }
    }

    isProjectNature() {
        return (
            this.getSrvcConfigData()?.srvc_type_nature ==
            PROJECT_BASED_SERVICE_TYPE
        );
    }

    getPossibleStatuses(item, type_id_vs_config) {
        if (this.isCustomerRequests()) {
            let matchingConfig = type_id_vs_config
                ? this.getMatchingConfigFrmItem(type_id_vs_config, item)
                : this.state.editorItemConfig;
            // return api_resp.
            if (matchingConfig) {
                return this.getStatusesFrmConfigJSON(matchingConfig);
            } else {
                return undefined;
            }
        } else {
            return this.state.viewData?.filters_proto?.statuses;
        }
    }

    getStatusesFrmConfigJSON(matchingConfig) {
        // console.log('matchingConfigjson',matchingConfig)
        let statuses_in_form_data = JSON.parse(
            matchingConfig.srvc_config.srvc_statuses
        );
        let statuses = [];
        Object.keys(statuses_in_form_data).map((singleCategory) => {
            let statusesInCategory = statuses_in_form_data[singleCategory];
            statusesInCategory = statusesInCategory.map((singleStatus) => {
                let newStatus = singleStatus;
                newStatus.value = singleStatus.key;
                newStatus.label = singleStatus.title;
                return newStatus;
            });
            statuses = [...statuses, ...statusesInCategory];
        });
        // console.log('decoded statuses',JSON.stringify(statuses));
        return statuses;
    }

    refresh() {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    }

    getSrvcTypeIdVsConfig() {
        return this.isCustomerRequests()
            ? this.state.srvc_type_id_vs_config
            : [];
    }

    getSrvcReqSelectedColumns(srvc_req_form_key, item) {
        let column = '';
        if (srvc_req_form_key == 'srvc_prvdr') {
            column = (
                <div>
                    {!this.isCustomerRequests() ? (
                        <p
                            className={`${item.is_deleted == true ? 'gx-text-grey' : 'gx-text-primary'}`}
                        >
                            {this.getPrvdrByKey(item.srvc_prvdr)?.label}
                        </p>
                    ) : (
                        this.getOrgLabelCmpnt(item)
                    )}
                </div>
            );
        } else if (srvc_req_form_key == 'request_description') {
            column = (
                <p
                    className={`${item.is_deleted == true ? 'gx-text-grey' : 'gx-mr-3 '}`}
                >
                    <Tooltip title={item.description}>
                        <Paragraph ellipsis={{ rows: 2 }}>
                            {item.description}{' '}
                        </Paragraph>
                    </Tooltip>
                </p>
            );
        } else if (srvc_req_form_key == 'cust_full_name') {
            column = <p className=""> {item.cust_name} </p>;
        } else if (srvc_req_form_key == 'full_address') {
            column = (
                <div className="ant-row-flex ">
                    <p className="">
                        {item.addr && item.addr != '' && (
                            <span className="gx-d-block gx-ml-3">
                                <Tooltip title={item.addr}>
                                    {item.addr.length < 20 ? (
                                        item.addr
                                    ) : (
                                        <span>
                                            {item.addr.substring(0, 10)}
                                            ...
                                            {item.addr.substring(
                                                item.addr.length - 20
                                            )}
                                        </span>
                                    )}
                                </Tooltip>
                            </span>
                        )}
                    </p>
                </div>
            );
        } else if (srvc_req_form_key == 'sbtsks') {
            column = (
                <div>
                    {item.sbtsks_meta && (
                        <div className="ant-row-flex">
                            <p className="gx-text-primary gx-width-175">
                                <Avatar.Group
                                    maxCount={2}
                                    className="gx-d-inline-flex gx-vertical-align-middle gx-mr-2"
                                >
                                    {item.sbtsks_meta.map(
                                        (singleSubtaskMeta, index) => (
                                            <Tooltip
                                                key={index}
                                                title={`${singleSubtaskMeta.sbtsk_type_title} -
                                                        ${singleSubtaskMeta.sbtsk_status}`}
                                                placement="top"
                                            >
                                                <Avatar
                                                    className={`${item.is_deleted == true ? 'gx-bg-grey' : ' '}`}
                                                    style={{
                                                        backgroundColor:
                                                            singleSubtaskMeta.sbtsk_status_color,
                                                    }}
                                                    icon={
                                                        <i
                                                            className={`icon ${singleSubtaskMeta.sbtsk_type_icon} gx-fs-md`}
                                                        />
                                                    }
                                                >
                                                    {/* {singleSubtaskMeta.sbtsk_type_title} */}
                                                </Avatar>
                                            </Tooltip>
                                        )
                                    )}
                                </Avatar.Group>
                                -{' '}
                                <span
                                    className={`${item.is_deleted == true ? 'gx-text-grey' : ''}`}
                                >
                                    {item.sbtsks_meta.length}{' '}
                                </span>
                            </p>
                        </div>
                    )}
                </div>
            );
        } else if (srvc_req_form_key == 'attachments') {
            column = (
                <div className="gx-d-flex gx-align-items-center">
                    {item.Attachment_count && (
                        <p className="gx-mb-auto">
                            <LinkOutlined /> {item.Attachment_count}
                        </p>
                    )}
                </div>
            );
        } else if (srvc_req_form_key == 'request_priority') {
            column = (
                <p className="">
                    <p
                        className={`${item.is_deleted == true ? 'gx-text-grey' : `gx-border gx-menu-horizontal gx-p-1 gx-text-center ${getTextColorFrPriority(item.priority)}`}`}
                    >
                        {item.priority}
                    </p>
                </p>
            );
        } else if (srvc_req_form_key == 'creation_date') {
            column = (
                <div className="ant-row-flex">
                    <p className="">{convertUTCToDisplayTime(item.c_time)}</p>
                </div>
            );
        } else if (srvc_req_form_key == 'request_req_date') {
            column = (
                <div className="ant-row-flex">
                    <p className="">
                        {convertUTCToDisplayTime(item.req_date, true)}
                    </p>
                </div>
            );
        } else if (srvc_req_form_key?.includes('authority_')) {
            const srvc_req_authority_val = this.getSrvcReqFields(
                srvc_req_form_key,
                item
            )?.[srvc_req_form_key];
            if (srvc_req_authority_val) {
                column = (
                    <div>
                        <UserName id={srvc_req_authority_val} />
                    </div>
                );
            }
        } else {
            column = (
                <p className="">
                    {item?.[srvc_req_form_key] ||
                        this.getSrvcReqFields(srvc_req_form_key, item)?.[
                            srvc_req_form_key
                        ]}
                </p>
            );
        }
        return column;
    }

    getSrvcReqFields(srvc_req_form_key, item) {
        let config_data = this.isCustomerRequests()
            ? this.state.viewData?.config_data?.vertical_form_data
            : this.state.viewData?.config_data;

        let customFields = this.isCustomerRequests()
            ? decodeFieldsMetaFrmJson(config_data?.sp_cust_fields_json)
            : decodeFieldsMetaFrmJson(config_data?.srvc_cust_fields_json);

        let getSrvcReqFieldMeta = [
            ...getCustomerInfoMeta().fields,
            ...getAddressInfoMeta().fields,
            ...getRequestInfoMeta().fields,
            ...customFields,
            ...getAuthorityLabelValuePairsWithPrefix(
                this.state.viewData?.filters_proto?.srvc_authorities_list
            ),
        ];
        let srvcReqFieldObj = {};
        let srvc_req_custom_field = item?.fields_data_for_table;
        if (srvc_req_custom_field && srvc_req_custom_field.length > 0) {
            srvc_req_custom_field.forEach((singleSrvcReqcustField) => {
                let valueDataFrmFormMeta = getValueDataFrmFormMeta(
                    getSrvcReqFieldMeta,
                    singleSrvcReqcustField
                );
                if (valueDataFrmFormMeta?.[srvc_req_form_key]) {
                    srvcReqFieldObj[srvc_req_form_key] =
                        valueDataFrmFormMeta[srvc_req_form_key];
                }
                //Request details me jo CC users and Labels hai woh abhi nhi aa rha hai. config me jo tabular view hai waha se hide kiya hai.
                //Agar chahiye to pehle waha se enable karna padega or fir open below code and array pe loop laga k value lana hai.
                // if(typeof valueDataFrmFormMeta?.[srvc_req_form_key] == 'object'){
                //     srvcReqFieldObj[srvc_req_form_key] = valueDataFrmFormMeta[srvc_req_form_key].toString();
                // }else{
                //     srvcReqFieldObj[srvc_req_form_key] = valueDataFrmFormMeta[srvc_req_form_key];
                // }
            });
        }
        return srvcReqFieldObj;
    }

    getSrvcReqTblColumns() {
        let deleteBtn = [];
        if (!this.isCustomerRequests()) {
            deleteBtn.push({
                key: 'delete_btn',
                title: 'Action',
                render: (text, item) => {
                    return (
                        <div className="gx-featured-content-right">
                            <div className="gx-d-flex gx-align-items-center">
                                <Dropdown
                                    overlay={
                                        <Space>
                                            <div>
                                                <DeleteSrvcReqButton
                                                    serviceReqDetails={item}
                                                    srvcId={this.state.srvc_id}
                                                    urlToSubmit={
                                                        '/services/modify/' +
                                                        item.srvc_type_id +
                                                        '/' +
                                                        item.id
                                                    }
                                                    onDataModified={(
                                                        entry_id
                                                    ) => {
                                                        this.notifyDataSetChanged(
                                                            entry_id
                                                        );
                                                    }}
                                                />
                                            </div>
                                        </Space>
                                    }
                                    // trigger={['click']}
                                >
                                    <a
                                        className="gx-p-1"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}
                                        style={{ zIndex: '5' }}
                                    >
                                        <span className="gx-fs-11 ">
                                            {' '}
                                            <i className="icon-ellipse-v " />
                                        </span>
                                    </a>
                                </Dropdown>
                            </div>
                        </div>
                    );
                },
            });
        }

        let columns = [
            ...deleteBtn,
            {
                key: 'status',
                dataIndex: 'status',
                title: 'Status',
                render: (record, item) => {
                    return (
                        <StatusChanger
                            possibleStatus={this.getPossibleStatuses(
                                item,
                                this.getSrvcTypeIdVsConfig()
                            )}
                            currentStatus={item.status.key}
                            srvc_id={this.state.srvc_id}
                            deleted={item.is_deleted}
                            // urlToSubmit={this.submitUrl + "/" + this.props.editorItem.id}
                            urlToSubmit={
                                '/services/modify/' +
                                item.srvc_type_id +
                                '/' +
                                item.id
                            }
                            onDataModified={(entry_id) => {
                                // refresh subtasks list
                                this.notifyDataSetChanged(item.id, false);
                            }}
                        />
                    );
                },
            },
            ...this.getStatusTxnColBasedOnConfig(),
            {
                key: 'title',
                dataIndex: 'title',
                title: 'Request ID',
                render: (record, item) => {
                    return (
                        <p className="gx-mb-2 gx-text-grey">
                            {showExtOrderId(this.getSrvcTypeConfig(item)) &&
                            item?.ext_order_id
                                ? item?.ext_order_id
                                : item.title}{' '}
                        </p>
                    );
                },
            },
        ];
        //Selected tabular_view_columns from config
        let config_data = this.isCustomerRequests()
            ? this.state.viewData?.config_data?.vertical_form_data
            : this.state.viewData?.config_data;

        let tabular_view_columns =
            config_data?.srvc_type_tabular_view_columns ||
            addDefaultKeysForTabularView();
        let srvcReqSelectedColumns = getDefaultColumnsForTabularView();
        srvcReqSelectedColumns = srvcReqSelectedColumns.filter(
            (col) => col.key != 'status_transition_date'
        );
        let customFields = this.isCustomerRequests()
            ? decodeFieldsMetaFrmJson(config_data?.sp_cust_fields_json)
            : decodeFieldsMetaFrmJson(config_data?.srvc_cust_fields_json);

        if (tabular_view_columns) {
            let getFieldsMetaFrTabularView = [
                ...getCustomerInfoMeta().fields,
                ...getAddressInfoMeta().fields,
                ...getRequestInfoMeta().fields,
                ...srvcReqSelectedColumns,
                ...customFields,
                ...getAuthorityLabelValuePairsWithPrefix(
                    this.state.viewData?.filters_proto?.srvc_authorities_list
                ),
            ];

            srvcReqSelectedColumns = [];
            tabular_view_columns.forEach((singleColumn) => {
                let filteredSelectedColumn = getFieldsMetaFrTabularView.filter(
                    (singleTabularViewColumn) =>
                        singleTabularViewColumn.key == singleColumn
                )?.[0];

                if (filteredSelectedColumn) {
                    srvcReqSelectedColumns.push(filteredSelectedColumn);
                }
            });
        }

        if (
            !this.state.isLoadingViewData &&
            srvcReqSelectedColumns &&
            srvcReqSelectedColumns.length > 0
        ) {
            srvcReqSelectedColumns.forEach((singleColumn) => {
                if (singleColumn) {
                    columns.push({
                        key: singleColumn.key,
                        dataIndex: singleColumn.key,
                        title:
                            singleColumn.key == 'srvc_prvdr' &&
                            this.isCustomerRequests()
                                ? 'Brand Name'
                                : singleColumn.label,
                        render: (record, item) => {
                            return this.getSrvcReqSelectedColumns(
                                singleColumn.key,
                                item
                            );
                        },
                    });
                }
            });
        }
        return columns;
    }
    getStatusTxnColBasedOnConfig() {
        //console.log('srvc_type_config', this.getSrvcTypeConfig().srvc_type_tabular_view_columns );
        let srvc_type_tabular_view_columns =
            this.getSrvcTypeConfig()?.srvc_type_tabular_view_columns;
        console.log(
            srvc_type_tabular_view_columns,
            'srvc_type_tabular_view_columns'
        );
        if (
            srvc_type_tabular_view_columns?.includes('status_transition_date')
        ) {
            return [
                {
                    key: 'status_transition_date',
                    dataIndex: 'status_transition_date',
                    title: 'Transition Date',
                    render: (record, item) => {
                        return (
                            <p className="gx-mb-2 gx-text-grey">
                                {showExtOrderId(this.getSrvcTypeConfig(item)) &&
                                item?.ext_order_id
                                    ? item?.ext_order_id
                                    : convertUTCToDisplayTime(
                                          item.status?.transition_date,
                                          true,
                                          false
                                      )}{' '}
                            </p>
                        );
                    },
                },
            ];
        }
        return [];
    }

    handleOnCloseItemEditor() {
        this.setState({
            showItemEditor: false,
        });
        if (this.state.autoOpenItemEditor) {
            this.props.history.goBack();
        }
    }

    getSrvcTypeConfig(item = {}) {
        const config_data = this.isCustomerRequests()
            ? this.state.srvc_type_id_vs_config?.find((singleSrvcTypeId) => {
                  return singleSrvcTypeId.srvc_id == item.srvc_type_id;
              })?.srvc_config
            : this.state.viewData?.config_data;
        return config_data;
    }

    displayAvgGaiRatingOfSubTasks(data) {
        if (!data) return;
        const avgGaiRating = getAvgGaiRatingOfSubTasks(data);
        if (!avgGaiRating) return;

        return (
            <div>
                <GaiWrapper>
                    <div className="gx-d-flex gx-justify-content-start gx-align-items-center wy-gap-10 gx-fs-sm">
                        <div className="gx-d-flex gx-align-items-center">
                            <img
                                src="https://static.wify.co.in/images/website/tms/star.png"
                                className="wy-rating-img-small"
                            />
                            <div className="gx-ml-1 gx-fs-sm ">
                                {avgGaiRating}
                            </div>
                        </div>
                        <div>
                            <i>GenAI Score</i>{' '}
                        </div>
                    </div>
                </GaiWrapper>
            </div>
        );
    }

    displayAvgGaiRatingOfSubTasks(data) {
        if (!data) return;
        const avgGaiRating = getAvgGaiRatingOfSubTasks(data);
        if (!avgGaiRating) return;

        return (
            <div>
                <GaiWrapper>
                    <div className="gx-d-flex gx-justify-content-start gx-align-items-center wy-gap-10 gx-fs-sm">
                        <div className="gx-d-flex gx-align-items-center">
                            <img
                                src="https://static.wify.co.in/images/website/tms/star.png"
                                className="wy-rating-img-small"
                            />
                            <div className="gx-ml-1 gx-fs-sm ">
                                {avgGaiRating}
                            </div>
                        </div>
                        <div>
                            <i>GenAI Score</i>{' '}
                        </div>
                    </div>
                </GaiWrapper>
            </div>
        );
    }

    getRenderItemsForTicketsInQuickAssign = (item, api_resp) => {
        const address = formatAddressWithoutPincode(item);
        return (
            <div className="gx-position-relative gx-w-100">
                <div className="gx-d-flex gx-flex-column gx-justify-content-between">
                    <>
                        <StatusChanger
                            possibleStatus={this.getPossibleStatuses(
                                item,
                                api_resp.type_id_vs_config
                            )}
                            currentStatus={item.status.key}
                            srvc_id={this.state.srvc_id}
                            deleted={item.is_deleted}
                            // urlToSubmit={this.submitUrl + "/" + this.props.editorItem.id}
                            urlToSubmit={
                                '/services/modify/' +
                                item.srvc_type_id +
                                '/' +
                                item.id
                            }
                            onDataModified={(entry_id) => {
                                // refresh subtasks list
                                this.notifyDataSetChanged(item.id, false);
                            }}
                        />
                    </>
                    <div className="gx-d-flex gx-align-items-center gx-justify-content-between gx-p-2 gx-w-100">
                        <div>
                            <div className="gx-d-flex gx-justify-content-start gx-align-items-center wy-fs-12">
                                <i className="icon icon-user-o gx-d-block gx-mr-1"></i>
                                <div className="wy-minus-margin-top-1">
                                    {item.cust_name}{' '}
                                </div>
                            </div>
                            <div className="gx-d-flex gx-justify-content-start gx-align-items-start gx-my-1 wy-fs-12">
                                <i className="icon icon-geo-location gx-d-block gx-mr-1"></i>
                                <div className="wy-minus-margin-top-1">
                                    {item.cust_pincode && (
                                        <div className="gx-d-flex gx-flex-row">
                                            {item.cust_pincode && (
                                                <>
                                                    <b>{item.cust_pincode}</b>
                                                    {address && (
                                                        <span className="gx-mx-1">
                                                            -
                                                        </span>
                                                    )}
                                                </>
                                            )}
                                            <Tooltip title={address}>
                                                <Text
                                                    ellipsis={
                                                        this.state.ellipsis
                                                    }
                                                    style={
                                                        this.state.ellipsis && {
                                                            width: 150,
                                                        }
                                                    }
                                                >
                                                    {address}
                                                </Text>
                                            </Tooltip>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="wy-fs-12">
                                <div className="gx-d-flex gx-justify-content-start  gx-align-items-start">
                                    <p className="gx-d-flex gx-justify-content-start  gx-align-items-start">
                                        <i
                                            className={`icon icon-timepicker  gx-mr-1`}
                                        />
                                        {convertUTCToDisplayTime(
                                            item.req_date,
                                            true
                                        )}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="wy-fs-12">
                            <div className="gx-mt-0 gx-mb-1 gx-d-flex gx-justify-content-end">
                                <div className="gx-card gx-mb-0 gx-border gx-border-blue gx-px-2 gx-py-1 gx-d-flex gx-align-items-center gx-w-fit-content">
                                    <img
                                        className={`gx-mr-1 gx-vertical-align-bottom wy-object-contain wy-h-15`}
                                        src={item.org.icon_path}
                                    ></img>
                                    {item.org.label}
                                </div>
                            </div>
                            <div className="wy-fs-12">
                                <p className="gx-mb-2 gx-text-grey gx-d-flex gx-justify-content-end">
                                    {showExtOrderId(
                                        this.getSrvcTypeConfig(item)
                                    ) && item?.ext_order_id
                                        ? item?.ext_order_id
                                        : item.title}
                                </p>
                                <div className="wy-fs-12 gx-text-success gx-d-flex gx-justify-content-end">
                                    {formatTimeRange(
                                        item.req_start_time,
                                        item.req_end_time
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="wy-quick-assign-description-wrapper">
                        <div
                            className={`${item.is_deleted == true ? 'gx-text-grey' : ' gx-mb-1'} wy-fs-12 gx-mb-0`}
                        >
                            <Tooltip title={item.description} placement="left">
                                <Paragraph
                                    ellipsis={{ rows: 2 }}
                                    className="gx-mb-0"
                                >
                                    {item.description}{' '}
                                </Paragraph>
                            </Tooltip>
                        </div>
                    </div>
                    {/* <div className="gx-dot-arrow gx-d-md-flex gx-d-none">
                        <div
                            className={
                                `${item.is_deleted == true ? 
                                    "gx-bg-grey gx-hover-arrow"
                                : 
                                    "gx-bg-primary gx-hover-arrow"}`
                            }
                        >
                            <i className="icon icon-long-arrow-right gx-text-white" />
                        </div>
                    </div> */}
                </div>
            </div>
        );
    };

    getRenderListItem = (item, api_resp) => {
        const feedbackRatingFieldKey = item.feedback_data?.rating_field_key;
        const ratingMeta = item.feedback_data?.form_meta?.find(
            (item) => item.key === feedbackRatingFieldKey
        );
        const userFeedbackData = {
            feedbackRatingFieldKey,
            rated: item.feedback_data?.form_data?.[feedbackRatingFieldKey] || 0,
            maxRating: ratingMeta?.widgetProps?.count,
        };
        return (
            <>
                <div className="gx-featured-content-left">
                    <div className="gx-d-flex">
                        <StatusChanger
                            possibleStatus={this.getPossibleStatuses(
                                item,
                                api_resp.type_id_vs_config
                            )}
                            currentStatus={item.status.key}
                            srvc_id={this.state.srvc_id}
                            deleted={item.is_deleted}
                            // urlToSubmit={this.submitUrl + "/" + this.props.editorItem.id}
                            urlToSubmit={
                                '/services/modify/' +
                                item.srvc_type_id +
                                '/' +
                                item.id
                            }
                            onDataModified={(entry_id) => {
                                // refresh subtasks list
                                this.notifyDataSetChanged(item.id, false);
                            }}
                        />
                        {item.feedback_data && (
                            <StarRatingCompact
                                rated={userFeedbackData?.rated}
                                maxRating={userFeedbackData?.maxRating}
                            />
                        )}
                    </div>

                    {this.isQuickAssign() && (
                        <p className="gx-mt-0 gx-mb-1">
                            <div className="gx-card gx-border gx-border-blue gx-px-2 gx-py-1 gx-d-flex">
                                <Avatar
                                    className={`gx-size-20 gx-mr-2 gx-vertical-align-bottom `}
                                    src={item.org.icon_path}
                                ></Avatar>
                                {item.org.label}
                            </div>
                        </p>
                    )}
                    <p className="gx-mb-2 gx-text-grey">
                        {showExtOrderId(this.getSrvcTypeConfig(item)) &&
                        item?.ext_order_id
                            ? item?.ext_order_id
                            : item.title}
                        <sup
                            className={`${item.is_deleted == true ? 'gx-text-grey' : `${getTextColorFrPriority(item.priority)}`}`}
                        >
                            ({item.priority})
                        </sup>
                    </p>

                    <h2
                        className={`${item.is_deleted == true ? 'gx-text-grey' : 'gx-mr-3 gx-mb-1'}`}
                    >
                        <Tooltip title={item.description}>
                            <Paragraph ellipsis={{ rows: 3 }}>
                                {item.description}{' '}
                            </Paragraph>
                        </Tooltip>
                    </h2>

                    {item.labels && item.labels.length > 0 && (
                        <div className="ant-row-flex gx-mt-3">
                            <p className="gx-mr-3 gx-mb-1 ">
                                {item.labels &&
                                    item.labels.map((singleLabel, index) => (
                                        <span
                                            className="gx-text-grey"
                                            key={index}
                                        >
                                            {index > 0 ? ' | ' : ''}
                                            {singleLabel.label}
                                        </span>
                                    ))}
                            </p>
                        </div>
                    )}

                    <div className="ant-row-flex gx-mt-1">
                        <p className="gx-text-grey gx-mb-1">
                            <i
                                className={`icon icon-user gx-fs-xs gx-mr-2 gx-d-inline-flex gx-vertical-align-sub`}
                            />
                            {item.cust_name} -{' '}
                            <small>
                                {convertUTCToDisplayTime(item.c_time)}
                            </small>
                            {item.addr && item.addr != '' && (
                                <span className="gx-d-block gx-ml-3">
                                    <Tooltip title={item.addr}>
                                        {item.addr.length < 20 ? (
                                            item.addr
                                        ) : (
                                            <span>
                                                {item.addr.substring(0, 10)}
                                                ...
                                                {item.addr.substring(
                                                    item.addr.length - 20
                                                )}
                                            </span>
                                        )}
                                    </Tooltip>
                                </span>
                            )}
                        </p>
                    </div>
                    <div className="ant-row-flex gx-mt-1">
                        <p className="  gx-mb-1">
                            <i
                                className={`icon icon-timepicker gx-fs-xs gx-mr-2 gx-d-inline-flex gx-vertical-align-sub`}
                            />
                            {convertUTCToDisplayTime(item.req_date, true)}
                        </p>
                    </div>
                    {item.sbtsks_meta && (
                        <div className="ant-row-flex gx-mt-1">
                            <p className="gx-text-primary">
                                <Avatar.Group
                                    maxCount={2}
                                    className="gx-d-inline-flex gx-vertical-align-middle gx-mr-2"
                                >
                                    {item.sbtsks_meta.map(
                                        (singleSubtaskMeta, index) => (
                                            <Tooltip
                                                key={index}
                                                title={`${singleSubtaskMeta.sbtsk_type_title} -  ${singleSubtaskMeta.sbtsk_status}`}
                                                placement="top"
                                            >
                                                <Avatar
                                                    className={`${
                                                        item.is_deleted == true
                                                            ? 'gx-bg-grey'
                                                            : ' '
                                                    }`}
                                                    style={{
                                                        backgroundColor:
                                                            singleSubtaskMeta.sbtsk_status_color,
                                                    }}
                                                    icon={
                                                        <i
                                                            className={`icon ${singleSubtaskMeta.sbtsk_type_icon} gx-fs-md`}
                                                        />
                                                    }
                                                >
                                                    {/* {singleSubtaskMeta.sbtsk_type_title} */}
                                                </Avatar>
                                            </Tooltip>
                                        )
                                    )}
                                </Avatar.Group>
                                -{' '}
                                <span
                                    className={`${item.is_deleted == true ? 'gx-text-grey' : ''}`}
                                >
                                    {item.sbtsks_meta.length} subtask(s)
                                </span>
                            </p>
                        </div>
                    )}
                    <div className="gx-d-flex gx-justify-content-start wy-gap-10 gx-align-items-center gx-w-100">
                        {!this.isCustomerRequests() && (
                            <p
                                className={`${item.is_deleted == true ? 'gx-text-grey' : 'gx-text-primary gx-mb-0'}`}
                            >
                                {this.getPrvdrByKey(item.srvc_prvdr)?.label}
                            </p>
                        )}
                        <div>
                            {ConfigHelpers.isServiceProvider() &&
                                this.displayAvgGaiRatingOfSubTasks(
                                    item?.['sbtsks_meta']
                                )}
                        </div>
                    </div>
                </div>
                <div className="gx-dot-arrow gx-d-md-flex gx-d-none">
                    <div
                        className={`${
                            item.is_deleted == true
                                ? 'gx-bg-grey gx-hover-arrow'
                                : 'gx-bg-primary gx-hover-arrow'
                        }`}
                    >
                        <i className="icon icon-long-arrow-right gx-text-white" />
                    </div>
                    <div className="gx-dot">
                        <i
                            className={`${
                                item.is_deleted == true
                                    ? 'gx-text-grey icon icon-ellipse-v'
                                    : 'icon icon-ellipse-v gx-text-primary'
                            }`}
                        />
                    </div>
                </div>
                <div className="gx-featured-content-right">
                    <div className="gx-d-flex gx-align-items-center">
                        <div className="gx-text-success gx-d-flex gx-align-items-center">
                            {this.isQuickAssign() ? (
                                item.req_start_time || item.req_end_time ? (
                                    <LuClock className="gx-mr-1" />
                                ) : (
                                    ' '
                                )
                            ) : (
                                ' '
                            )}
                            {this.isQuickAssign()
                                ? formatTimeRange(
                                      item.req_start_time,
                                      item.req_end_time
                                  )
                                : this.getOrgLabelCmpnt(item)}
                        </div>
                        {!this.isQuickAssign() && item.Attachment_count && (
                            <p className="gx-mt-1 gx-mb-auto gx-text-grey gx-pr-2">
                                <LinkOutlined /> {item.Attachment_count}
                            </p>
                        )}
                        {!this.isCustomerRequests() && (
                            <Dropdown
                                overlay={
                                    <Space>
                                        <div>
                                            <DeleteSrvcReqButton
                                                serviceReqDetails={item}
                                                srvcId={this.state.srvc_id}
                                                urlToSubmit={
                                                    '/services/modify/' +
                                                    item.srvc_type_id +
                                                    '/' +
                                                    item.id
                                                }
                                                onDataModified={(entry_id) => {
                                                    this.notifyDataSetChanged(
                                                        entry_id
                                                    );
                                                }}
                                            />
                                        </div>
                                    </Space>
                                }
                                trigger={['click']}
                            >
                                <a
                                    className="gx-p-1"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}
                                    style={{ zIndex: '5' }}
                                >
                                    <span className="gx-fs-11 ">
                                        {' '}
                                        <i className="icon-ellipse-v wy-hover-toggle" />
                                    </span>
                                </a>
                            </Dropdown>
                        )}
                    </div>
                </div>
            </>
        );
    };

    getSrvcReqListItem(item, api_resp) {
        return (
            <List.Item
                key={item.id}
                deleted={item.is_deleted}
                className={
                    item.is_deleted == true
                        ? 'wy-deleted-ticket'
                        : 'wy-cursor-pointer'
                }
                onClick={() => {
                    this.onSingleRowClick(item, api_resp.type_id_vs_config);
                }}
            >
                <Widget styleName="gx-card-full gx-dot-arrow-hover">
                    <div className="gx-card gx-border gx-p-2 gx-mb-1 gx-featured-content">
                        {!this.state.isLoadingViewData &&
                            this.getRenderListItem(item, api_resp)}
                    </div>
                </Widget>
            </List.Item>
        );
    }

    getSrvcReqListItemFrQuickAssign(item, api_resp) {
        const { onDrop } = this.props;
        return (
            <List.Item
                //ref={drop}
                key={item.id}
                deleted={item.is_deleted}
                className={
                    item.is_deleted == true
                        ? 'wy-deleted-ticket'
                        : 'wy-cursor-pointer'
                }
                onClick={() => {
                    this.onSingleRowClick(item, api_resp.type_id_vs_config);
                }}
            >
                {!this.state.isLoadingViewData && (
                    <Widget styleName="gx-card-full gx-dot-arrow-hover">
                        <DroppableWrapper
                            item={item}
                            api_resp={api_resp}
                            getRenderListItem={
                                this.getRenderItemsForTicketsInQuickAssign
                            }
                            onDrop={onDrop}
                        />
                    </Widget>
                )}
            </List.Item>
        );
    }

    configFrPagedApiListView = {
        dataSourceApi:
            dataUrl +
            '/' +
            this.state.srvc_id +
            '/' +
            (this.state.isCustAcces ? '1' : '0'),
        onApiRespChange: (api_resp, retrieve_count) => {
            //we show directly srvc req form_data when user click my task req id link
            //when length of data array is one and showitemeditor is true
            if (
                !retrieve_count &&
                api_resp.data.length == 1 &&
                this.getVisiblityOfItemEditor()
            ) {
                this.setState(
                    {
                        autoOpenItemEditor: true,
                    },
                    () => {
                        this.onSingleRowClick(
                            api_resp.data[0],
                            api_resp.type_id_vs_config
                        );
                    }
                );
            }

            if (this.props.forQuickAssign) {
                if (retrieve_count) {
                    this.props.reqPending(api_resp.pagination.total || 0);
                }
            }
            if (!retrieve_count) {
                // const newValue = _.cloneDeep(api_resp?.type_id_vs_config);
                this.setState({
                    srvc_type_id_vs_config: api_resp?.type_id_vs_config,
                });
            }
        },
        tableOnRowClick: (singleRowData) => this.tableOnRowClick(singleRowData),

        renderSingleItem: (item, api_resp) =>
            this.props.forQuickAssign
                ? this.getSrvcReqListItemFrQuickAssign(item, api_resp)
                : this.getSrvcReqListItem(item, api_resp),
    };

    notifyDataSetChanged(entry_id, checkEditorItem = true) {
        // console.log("Refresh list called in parent entry id - ",entry_id);
        if (checkEditorItem && this.state.editorItem == undefined) {
            // console.log("Editor item was undefined, refreshing list");
            // new entry was created, we will have to clear filters
            // refresh list
            this.resetFilter();
        } else {
            //refresh list if something edit from quick assign section
            if (this.props.forQuickAssign) {
                this.props.onDataModified();
            }
            // Refresh along with existing filters
            this.setState(
                {
                    activeFilters: { ...this.state.activeFilters },
                },
                () => this.initViewData(true)
            );
        }
    }

    getItemFrValueInFilters(column, value) {
        var defaultReturn = { title: 'Unknown', value: 'test' };
        this.getFilters().values[column].forEach((element) => {
            if (element.value == value) {
                defaultReturn = element;
            }
        });
        return defaultReturn;
    }

    getSortFieldMetaFilters() {
        return this.isCustomerRequests() ? [...filtersFrPrvdr] : [...filters];
    }
    getFilters() {
        var staticFilters = this.isCustomerRequests()
            ? [...filtersFrPrvdr]
            : [...filters];
        var defaultAnyMeta = getAnyObjectFrFilter();
        var emptyMeta = getEmptyObjectFrFilter();

        var filtersFrmViewData = this.state.viewData?.filters_proto;
        console.log('filtersFrmViewData', filtersFrmViewData);
        var can_cust_rate = false;
        let isGAIEnabledFrSbtsk = true;
        if (this.isCustomerRequests()) {
            can_cust_rate = true;
            let customFields = decodeFieldsMetaFrmJson(
                this.state.viewData?.config_data?.vertical_form_data
                    ?.sp_cust_fields_json,
                undefined,
                false,
                true,
                undefined,
                undefined,
                true
            );
            if (isArray(customFields)) {
                // In the below code we are setting disabled value to false becoz it is not editable in filter
                staticFilters.push(
                    ...customFields.map((singleCustomField) => {
                        if (singleCustomField.type == 'Barcode_scanner') {
                            singleCustomField.disabled = false;
                        }
                        return singleCustomField;
                    })
                );
            }
            var customFileFields = getCustomFileFieldsFilter(
                this.state.viewData?.config_data?.vertical_form_data
                    ?.sp_cust_fields_json
            );
            if (customFileFields) {
                staticFilters.push(...customFileFields);
            }
        } else {
            let customFields = decodeFieldsMetaFrmJson(
                this.state.viewData?.config_data?.srvc_cust_fields_json,
                undefined,
                false,
                true,
                undefined,
                undefined,
                true
            );
            if (isArray(customFields)) {
                // In the below code we are setting disabled value to false becoz it is not editable in filter
                staticFilters.push(
                    ...customFields.map((singleCustomField) => {
                        if (singleCustomField.type == 'Barcode_scanner') {
                            singleCustomField.disabled = false;
                        }
                        return singleCustomField;
                    })
                );
            }
            var customFileFields = getCustomFileFieldsFilter(
                this.state.viewData?.config_data?.srvc_cust_fields_json
            );
            if (customFileFields) {
                staticFilters.push(...customFileFields);
            }
            can_cust_rate =
                this.state.viewData?.config_data?.srvc_can_cust_rate;
            isGAIEnabledFrSbtsk =
                this.state.viewData?.config_data?.gai_rating_subtasks;
        }

        if (can_cust_rate) {
            staticFilters.push(...this.getFeedbackFilters());
        }
        if (isGAIEnabledFrSbtsk && ConfigHelpers.isServiceProvider()) {
            staticFilters.push(...this.getIsGaiRatingFilterEnabled());
        }
        if (isGAIEnabledFrSbtsk && ConfigHelpers.isServiceProvider()) {
            staticFilters.push(...this.getSbtskGaiRatingFieldMetaFrFilter());
        }

        var noOfTaskFilterMeta = getNoOfTasksObjectFrFilter();
        if (noOfTaskFilterMeta) {
            staticFilters.push(noOfTaskFilterMeta);
        }

        var priorityFilterMeta = getPriorityObjectFrFilter();
        if (priorityFilterMeta) {
            staticFilters.push(priorityFilterMeta);
        }

        var IsDeletedFilterMeta = getIsDeletedFrFilter();
        if (IsDeletedFilterMeta) {
            staticFilters.push(IsDeletedFilterMeta);
        }

        //get Authorities filters
        let authoritiesFilters = this.getAuthoritiesFilters(filtersFrmViewData);
        if (isArray(authoritiesFilters)) {
            staticFilters.push(...authoritiesFilters);
        }
        //get srvc provider authorities filters
        let srvcPrvdrAuthoritiesFilters =
            this.getSrvcPrvdrAuthoritiesFilters(filtersFrmViewData);
        if (isArray(srvcPrvdrAuthoritiesFilters)) {
            staticFilters.push(...srvcPrvdrAuthoritiesFilters);
        }
        let statusTransitionDateFilters = this.getStatusTransitionFilters(
            this.state.viewData?.filters_proto?.statuses
        );
        if (isArray(statusTransitionDateFilters)) {
            staticFilters.push(...statusTransitionDateFilters);
        }
        // console.log('customFields',customFields)
        var finalFilter = getFilterFormMetaFilledWithApiData(
            staticFilters,
            defaultAnyMeta,
            filtersFrmViewData,
            emptyMeta
        );
        let srvcColumn =
            this.state.viewData?.config_data?.vertical_form_data
                ?.srvc_type_view_columns ||
            this.state.viewData?.config_data?.select_brand_filetrs;
        if (srvcColumn?.length > 0) {
            finalFilter = finalFilter.filter(
                (singleFilterObject) =>
                    srvcColumn?.includes(singleFilterObject.key) ||
                    singleFilterObject.key == 'verticals_list'
            );
        }
        return finalFilter;
        // let newFinalFilter=[]
        // if(this.state.viewData?.config_data?.select_brand_filetrs?.length>0){
        //     newFinalFilter = finalFilter.filter(
        //         value=>this.state.viewData?.config_data?.select_brand_filetrs.includes(value.key)
        //     )
        //     return newFinalFilter
        // }else {
        //     return finalFilter;
        // }
    }

    getStatusTransitionFilters(statuses = []) {
        const statusTransitionDateFilters = statuses.map((status) => {
            return {
                key: `${status.value}_transition_date`,
                label: `${status.label} transition date`,
                widget: DatePicker.RangePicker,
                widgetProps: {
                    ranges: getPresetRangesForRangeDatePicker(),
                    disabledDate: (current) =>
                        current && current > moment().endOf('day'),
                },
            };
        });

        return statusTransitionDateFilters;
    }

    getAuthoritiesFilters(filtersFrmViewData) {
        let authoritiesFilters = [];
        if (filtersFrmViewData?.srvc_authorities_list) {
            filtersFrmViewData.srvc_authorities_list.forEach(
                (singleAuthoritiesFilters) => {
                    let filterObj = {
                        key:
                            'authority_' +
                            singleAuthoritiesFilters.value.toString(),
                        label: singleAuthoritiesFilters.label,
                        options: [
                            getEmptyObjectFrFilter(),
                            getAnyObjectFrFilter(),
                        ].concat(singleAuthoritiesFilters.authorities_name),
                        required: false,
                        quick: true,
                        widgetProps: {
                            mode: 'multiple',
                            showSearch: true,
                            optionFilterProp: 'children',
                        },
                        widget: 'select',
                    };
                    authoritiesFilters.push(filterObj);
                }
            );
        }
        return authoritiesFilters;
    }

    getSrvcPrvdrAuthoritiesFilters(filtersFrmViewData) {
        let srvcPrvdrAuthoritiesFilters = [];
        if (filtersFrmViewData?.srvc_prvdr_authorities_users_list) {
            filtersFrmViewData.srvc_prvdr_authorities_users_list.forEach(
                (singleAuthoritiesFilters) => {
                    let filterObj = {
                        key:
                            'authority_' +
                            singleAuthoritiesFilters.value.toString(),
                        label: 'SP ' + singleAuthoritiesFilters.label,
                        options: singleAuthoritiesFilters.authorities_name,
                        required: false,
                        // "quick" : true,
                        widgetProps: {
                            mode: 'multiple',
                            showSearch: true,
                            optionFilterProp: 'children',
                        },
                        widget: 'select',
                    };
                    srvcPrvdrAuthoritiesFilters.push(filterObj);
                }
            );
        }
        return srvcPrvdrAuthoritiesFilters;
    }

    getTabularViewPreferenceKey() {
        let tabularViewKey = 'srvc_type_id_' + this.state.srvc_id;
        if (this.state.srvc_id == 0) {
            tabularViewKey = 'customer_request';
        }
        return tabularViewKey;
    }
    getSearchbarPlaceholder() {
        const { activeFilters } = this.state;

        if (
            ConfigHelpers.isServiceProvider() &&
            activeFilters.verticals_list &&
            activeFilters.verticals_list.length > 0
        ) {
            return 'Search by Custom Fields, ID, Customer Name, Mobile and Description..';
        }

        return 'Search by ID, Customer Name, Mobile and Description..';
    }

    getColumns() {
        if (this.isQuickAssign()) {
            return 1; // Assuming 1 is returned if this.isQuickAssign() is true
        } else {
            return getIsTabularView(this.getTabularViewPreferenceKey())
                ? this.getSrvcReqTblColumns()
                : 2;
        }
    }

    renderAppModuleHeader = () => (
        <AppModuleHeader
            placeholder={this.getSearchbarPlaceholder()}
            currValue={this.state.searchFilter}
            optionsMenuData={
                !this.isQuickAssign() ? this.getExportMenuData() : ''
            }
            onChange={this.handleSearchChange}
            showTabularViewOption={!this.isQuickAssign()}
            tabularViewPreferenceKey={this.getTabularViewPreferenceKey()}
            tellParentToRefreshList={() => this.refresh()}
        />
    );

    renderSideBar = ({ activeFilters }) => (
        <SideBar
            filters={this.getFilters()}
            onFilterChange={(newFilterObject) =>
                this.handleFilterChange(newFilterObject)
            }
            activeFilters={activeFilters}
            onAddClick={this.onAddItem}
            srvcDetails={this.serviceTypeDetails}
            isCustomerRequests={this.isCustomerRequests()}
            isCustAcces={this.state.isCustAcces}
            staticFilters={this.getSortFieldMetaFilters()}
            onAddBulkAssign={this.onAddBulkAssign}
            isSrvcTypeNatureProject={this.isProjectNature()}
            onBulkCreate={this.onBulkCreate}
        />
    );

    render() {
        const {
            activeFilters,
            drawerState,
            showExporter,
            showEditor,
            editorItem,
            searchFilter,
            isLoadingViewData,
            showItemEditor,
            error,
            viewData,
            showBulkAssignEditor,
            showBulkCreator,
            pagedApi,
        } = this.state;
        console.log(
            'this.state.viewData?.config_data :: ',
            this.state.viewData?.config_data
        );
        const endDate = moment();
        const startDate = moment().subtract(5, 'months');
        const shouldApplyFiltersFrPrvdr =
            !activeFilters.assgn_to_prvdr_date &&
            ConfigHelpers.isServiceProvider() &&
            !this.state.isCustAcces &&
            (!this.getSearchFromUrl() || this.getSearchFromUrl() === '');

        if (shouldApplyFiltersFrPrvdr) {
            activeFilters.assgn_to_prvdr_date = [startDate, endDate];
        }

        const shouldApplyFilters =
            !activeFilters.creation_srvc_req_date &&
            (!ConfigHelpers.isServiceProvider() || this.state.isCustAcces) &&
            (!this.getSearchFromUrl() || this.getSearchFromUrl() === '') &&
            !activeFilters.statuses;

        if (shouldApplyFilters) {
            activeFilters.creation_srvc_req_date = [startDate, endDate];
        }
        if (this.state.isLoadingViewData && !this.isCustomerRequests()) {
            return (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            );
        }
        // console.log("Service type details : ", this.serviceTypeDetails);
        return (
            <>
                {/* <div>
                    Hi this is service id - {this.state.srvc_id}
                    <br></br>
                    You can - {JSON.stringify(this.userRights)}
                </div> */}
                {error != '' ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <div
                        className={`${this.isQuickAssign() && 'wy-quick-assign'} gx-main-content`}
                    >
                        <div className="gx-app-module">
                            {!this.isQuickAssign() && (
                                <div className="gx-d-block gx-d-lg-none">
                                    <Drawer
                                        placement="left"
                                        closable={false}
                                        visible={drawerState}
                                        onClose={this.onToggleDrawer.bind(this)}
                                    >
                                        {this.renderSideBar({
                                            activeFilters,
                                        })}
                                    </Drawer>
                                </div>
                            )}
                            {(isLoadingViewData || !this.isQuickAssign()) && (
                                <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                                    {ConfigHelpers.isServiceProvider() &&
                                    this.isCustomerRequests() ? (
                                        isLoadingViewData ? (
                                            <div className="gx-module-side">
                                                <div>
                                                    <div className="gx-module-side-header">
                                                        {this.isCustomerRequests() && (
                                                            <div className="gx-module-logo">
                                                                <i
                                                                    className={`icon icon-breadcrumb gx-mr-2`}
                                                                />
                                                                <span>
                                                                    Customer
                                                                    requests
                                                                </span>
                                                            </div>
                                                        )}
                                                    </div>
                                                    <MiniSidebarSkeletonLoader
                                                        count={2}
                                                        listCount={[3, 3]}
                                                        singleButton
                                                    />
                                                </div>
                                            </div>
                                        ) : (
                                            !isLoadingViewData &&
                                            this.renderSideBar({
                                                activeFilters,
                                            })
                                        )
                                    ) : (
                                        this.renderSideBar({
                                            activeFilters,
                                        })
                                    )}
                                </div>
                            )}
                            {/* Creator popup */}
                            {!this.isCustomerRequests() && (
                                <ItemEditor
                                    srvcConfigData={viewData?.config_data}
                                    showEditor={showEditor}
                                    srvcDetails={this.serviceTypeDetails}
                                    onClose={() => {
                                        this.setState({ showEditor: false });
                                    }}
                                    onDataModified={(entry_id) => {
                                        this.notifyDataSetChanged(entry_id);
                                    }}
                                    isCustAcces={this.state.isCustAcces}
                                    sp_config_data={viewData?.sp_config_data}
                                    filters={activeFilters}
                                    srvc_id={this.state.srvc_id}
                                    orgSettingsData={
                                        viewData?.org_settings_data
                                    }
                                />
                            )}
                            {/* Exporter popup */}
                            {!this.isQuickAssign() && (
                                <ExporterModal
                                    srvcConfigData={viewData?.config_data}
                                    showEditor={showExporter}
                                    filters={activeFilters}
                                    srvcDetails={this.serviceTypeDetails}
                                    onClose={() => {
                                        this.setState({ showExporter: false });
                                    }}
                                    isCustAcces={this.state.isCustAcces}
                                    possibleStatus={this.getPossibleStatuses(
                                        editorItem
                                    )}
                                    isCustomerRequests={this.isCustomerRequests()}
                                    sp_config_data={viewData?.sp_config_data}
                                    sp_authority_data={
                                        viewData?.filters_proto
                                            ?.srvc_prvdr_authorities_users_list
                                    }
                                    brand_authority_data={
                                        viewData?.filters_proto
                                            ?.srvc_authorities_list
                                    }
                                    hidden_fields={
                                        viewData?.usr_srvc_hidden_field_ids[0]
                                            ?.hidden_srvc_fields
                                    }
                                    sp_hidden_fields={
                                        viewData?.usr_srvc_hidden_field_ids[0]
                                            ?.hidden_sp_custom_field
                                    }
                                />
                            )}
                            {!this.isCustomerRequests() && (
                                <BulkAssign
                                    showEditor={showBulkAssignEditor}
                                    srvcConfigData={viewData?.config_data}
                                    sbtaskTypeList={viewData?.sbtsk_types_list}
                                    srvcDetails={this.serviceTypeDetails}
                                    onDataModified={(entry_id) => {
                                        this.notifyDataSetChanged(entry_id);
                                    }}
                                    onClose={() => {
                                        this.setState({
                                            showBulkAssignEditor: false,
                                        });
                                    }}
                                    isCustAcces={this.state.isCustAcces}
                                    activeFilters={activeFilters}
                                    isSrvcTypeNatureProject={this.isProjectNature()}
                                />
                            )}
                            {this.isCustomerRequests() && (
                                <BulkCreate
                                    showEditor={showBulkCreator}
                                    srvcConfigData={viewData?.config_data}
                                    sbtaskTypeList={viewData?.sbtsk_types_list}
                                    srvcDetails={this.serviceTypeDetails}
                                    onDataModified={(entry_id) => {
                                        this.notifyDataSetChanged(entry_id);
                                    }}
                                    onClose={() => {
                                        this.setState({
                                            showBulkCreator: false,
                                        });
                                    }}
                                    verticalsList={
                                        this.state.viewData?.filters_proto
                                            ?.verticals_list
                                    }
                                    // isCustAcces = {this.state.isCustAcces}
                                    orgSettingsData={
                                        viewData?.org_settings_data
                                    }
                                />
                            )}

                            <div className="gx-module-box">
                                <div className="gx-module-box-header">
                                    <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                        {isLoadingViewData &&
                                        ConfigHelpers.isServiceProvider() &&
                                        this.isCustomerRequests() ? (
                                            <div>
                                                <Skeleton.Avatar
                                                    shape="square"
                                                    active
                                                    className="wy-br-5 gx-overflow-auto gx-mt-2"
                                                />
                                            </div>
                                        ) : (
                                            <i
                                                className="icon icon-filter gx-icon-btn"
                                                aria-label="Menu"
                                                onClick={this.onToggleDrawer.bind(
                                                    this
                                                )}
                                            />
                                        )}
                                    </span>

                                    {ConfigHelpers.isServiceProvider() &&
                                    this.isCustomerRequests() ? (
                                        isLoadingViewData ? (
                                            <div
                                                className="gx-d-flex gx-justify-content-end gx-align-items-center wy-gap-10"
                                                data-testid="app-module-header-skeleton"
                                            >
                                                <Skeleton.Avatar
                                                    shape="square"
                                                    active
                                                    className="wy-br-5 gx-overflow-auto"
                                                />
                                                <Skeleton.Avatar
                                                    shape="square"
                                                    className="wy-single-icon-loader"
                                                    active
                                                />
                                            </div>
                                        ) : (
                                            this.renderAppModuleHeader()
                                        )
                                    ) : (
                                        this.renderAppModuleHeader()
                                    )}
                                </div>
                                <div
                                    className={`${this.isQuickAssign() ? 'wy-srvc-overview-h-adjust-fr-quickassign wy-quick-assign-support-css' : 'gx-module-box-content gx-px-3 '} gx-py-3`}
                                >
                                    <CustomScrollbars>
                                        {pagedApi.dataLoading &&
                                            ConfigHelpers.isServiceProvider() &&
                                            this.isCustomerRequests() &&
                                            (getIsTabularView(
                                                this.getTabularViewPreferenceKey()
                                            ) ? (
                                                <div>
                                                    <CustomerRequestTicketTableView />
                                                </div>
                                            ) : (
                                                <div>
                                                    <CustomerRequestTicketCardView
                                                        numberOfCards={2}
                                                    />
                                                </div>
                                            ))}

                                        {ConfigHelpers.isServiceProvider() &&
                                        this.isCustomerRequests() &&
                                        (!isLoadingViewData ||
                                            !pagedApi.dataLoading) ? (
                                            <PagedApiListViewV2
                                                {...this
                                                    .configFrPagedApiListView}
                                                filterObject={
                                                    this.isQuickAssign()
                                                        ? this.props.filters
                                                        : activeFilters
                                                }
                                                searchQuery={searchFilter}
                                                overflowScrollbar
                                                columns={this.getColumns()}
                                                tableView={getIsTabularView(
                                                    this.getTabularViewPreferenceKey()
                                                )}
                                                skeletonLoading={true}
                                                onLoadingChange={
                                                    this
                                                        .handleLoadingChangeFromPagedApi
                                                }
                                            />
                                        ) : (
                                            (!isLoadingViewData ||
                                                !pagedApi.dataLoading) && (
                                                <PagedApiListViewV2
                                                    {...this
                                                        .configFrPagedApiListView}
                                                    filterObject={
                                                        this.isQuickAssign()
                                                            ? this.props.filters
                                                            : activeFilters
                                                    }
                                                    searchQuery={searchFilter}
                                                    overflowScrollbar
                                                    columns={this.getColumns()}
                                                    tableView={getIsTabularView(
                                                        this.getTabularViewPreferenceKey()
                                                    )}
                                                />
                                            )
                                        )}
                                        <ItemEditor
                                            showEditor={showItemEditor}
                                            onClose={() => {
                                                this.handleOnCloseItemEditor();
                                            }}
                                            onDataModified={(entry_id) => {
                                                this.notifyDataSetChanged(
                                                    entry_id
                                                );
                                            }}
                                            editMode={true}
                                            editorItem={editorItem}
                                            isCustomerRequests={this.isCustomerRequests()}
                                            srvcConfigData={this.getSrvcConfigData()}
                                            all_srvc_prvdrs={
                                                viewData?.all_srvc_prvdrs
                                            }
                                            srvcDetails={this.getSrvcDetails()}
                                            possibleStatus={this.getPossibleStatuses(
                                                editorItem
                                            )}
                                            isCustAcces={this.state.isCustAcces}
                                            sp_config_data={
                                                viewData?.sp_config_data
                                            }
                                            hidden_fields={
                                                viewData
                                                    ?.usr_srvc_hidden_field_ids[0]
                                                    ?.hidden_srvc_fields
                                            }
                                            sp_hidden_fields={
                                                viewData
                                                    ?.usr_srvc_hidden_field_ids[0]
                                                    ?.hidden_sp_custom_field
                                            }
                                            spAuthoritiesConfigData={
                                                viewData?.sp_authorities_config_data
                                            }
                                            filters={activeFilters}
                                            srvc_id={this.state.srvc_id}
                                            orgSettingsData={
                                                viewData?.org_settings_data
                                            }
                                            forQuickAssign={
                                                this.props.forQuickAssign
                                            }
                                        />
                                    </CustomScrollbars>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </>
        );
    }
}

ServiceOverview.propTypes = {};

export default ServiceOverview;
