const getServiceHubsModelFrQueue = (app, service_hubs_model_data) => {
    const service_hubs_model = require('../../../setup/capacity/service_hubs_model').getInstance();
    service_hubs_model.database = app.get('db');
    service_hubs_model.ip_addr = service_hubs_model_data.ip_address;
    service_hubs_model.user_agent = service_hubs_model_data.user_agent;
    service_hubs_model.user_context = service_hubs_model_data.userContext;
    return service_hubs_model;
};

module.exports = {
    getServiceHubsModelFrQueue,
};
