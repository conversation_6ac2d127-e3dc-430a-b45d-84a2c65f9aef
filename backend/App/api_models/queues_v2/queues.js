const allQueues = {
    WIFY_EG_QUEUE: {
        ons: {
            completed: (job, job_result) => {
                console.log('Completed EG job');
            },
            failed: (job, err) => {},
        },
        processor: '/example_processor',
    },
    WIFY_SRVC_REQ_CREATION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed srvc req creation workflow ');
            },
        },
        processor: '/srvc_req_creation_workflow',
    },
    WIFY_SRVC_REQ_STATUS_UPDATE_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed service status update workflow');
            },
        },
        processor: '/srvc_req_status_update_workflow',
    },
    WIFY_SRVC_REQ_UPDATE_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed service update workflow');
            },
        },
        processor: '/srvc_req_update_workflow',
    },
    WIFY_SEND_SMS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed send sms ');
            },
        },
        processor: '/send_sms',
    },
    WIFY_SEND_WHATSAPP_MSG: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed send whatsapp msg ');
            },
        },
        processor: '/send_whatsapp_msg',
    },
    WIFY_SRVC_REQ_LOC_MAPPING: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed loc mapping');
            },
        },
        processor: '/srvc_req_location_mapping',
    },
    WIFY_SRVC_REQ_GPS_VERIFICATION_OF_SUBTASKS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed lloc verifications of sub tasks');
            },
        },
        processor: '/srvc_req_loc_verification_subtasks',
    },
    WIFY_SBTSK_REQ_CREATION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed sbtsk req creation workflow ');
            },
        },
        processor: '/sbtsk_req_creation_workflow',
    },
    WIFY_SBTSK_REQ_STATUS_UPDATE_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed subtask status update workflow');
            },
        },
        processor: '/sbtsk_req_status_update_workflow',
    },
    WIFY_RESEND_OTP_TO_CUSTOMER: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed subtask status update workflow');
            },
        },
        processor: '/resend_otp_to_customer',
    },
    WIFY_SEND_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed send email ');
            },
        },
        processor: '/send_email',
    },
    WIFY_SRVC_REQ_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export srvc_req by email');
            },
        },
        processor: '/srvc_req_export_by_email',
    },
    WIFY_ASSIGNMENT_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export assignment by email');
            },
        },
        processor: '/assignment_export_by_email',
    },
    WIFY_OFFICE_ATTENDANCE_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export office attendance by email');
            },
        },
        processor: '/office_attendance_export_by_email',
    },
    WIFY_SITE_ATTENDANCE_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export site attendance by email');
            },
        },
        processor: '/site_attendance_export_by_email',
    },
    WIFY_AVAILABILITY_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    'Processed export office availability attendance by email'
                );
            },
        },
        processor: '/availability_export_by_email',
    },
    WIFY_VISIT_MAP_REQ_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export srvc_req by email');
            },
        },
        processor: '/visit_map_req_export_by_email',
    },
    WIFY_DAILY_REPORT_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export daily report by email');
            },
        },
        processor: '/daily_report_export_by_email',
    },
    WIFY_USERS_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export users by email');
            },
        },
        processor: '/users_export_by_email',
    },
    WIFY_RANGE_REPORT_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export range report by email');
            },
        },
        processor: '/range_report_export_by_email',
    },
    WIFY_ABSENT_USER_NOTIFY_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export range report by email');
            },
        },
        processor: '/absent_user_notify_by_email',
    },
    WIFY_RATE_CARD_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export rate card by email');
            },
        },
        processor: '/rate_card_export_by_email',
    },
    WIFY_LOC_GRP_CREATION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed location group creation workflow ');
            },
        },
        processor: '/loc_grp_creation_workflow',
    },
    WIFY_LOC_GRP_UPDATE_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed location group update workflow ');
            },
        },
        processor: '/loc_grp_update_workflow',
    },
    WIFY_UPDATE_SRVC_REQS_LOC_GRPS_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed update srvc reqs loc grps workflow ');
            },
        },
        processor: '/update_srvc_reqs_loc_grps_workflow',
    },
    WIFY_CREATE_API_LOGS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed create api logs ');
            },
        },
        processor: '/create_api_logs',
    },
    WIFY_LOGOUT_DEACTIVATED_USERS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed logout users after deactivation');
            },
        },
        processor: '/logout_deactivated_users',
    },
    WIFY_TMS_PULSE_TRACKER_GET_ROLE_SETTING: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    '[TMS_PULSE_TRACKER] Processed pulse tracker process org setting'
                );
            },
        },
        processor: '/pulse_tracker/get_role_setting',
    },
    WIFY_TMS_PULSE_TRACKER_GET_USERS_FRM_ORG_ROLE: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    '[TMS_PULSE_TRACKER] Processed pulse tracker get users from org & role'
                );
            },
        },
        processor: '/pulse_tracker/get_users_frm_org_role',
    },
    WIFY_TMS_PULSE_TRACKER_VALIDATE_USER_ATTENDANCE: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    '[TMS_PULSE_TRACKER] Processed pulse tracker validate attendance'
                );
            },
        },
        processor: '/pulse_tracker/validate_user_attendance',
    },
    WIFY_TMS_SEND_NOTIFICATION: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    '[TMS_PULSE_TRACKER] Processed pulse tracker send notification'
                );
            },
        },
        processor: '/send_notification',
    },
    WIFY_NOTIFICATION_SEND_FCM: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    '[TMS_PULSE_TRACKER] Processed pulse tracker fcm notification'
                );
            },
        },
        processor: '/send_fcm',
    },
    WIFY_GET_ALL_USERS_LAST_SEEN_FOR_COPY_TO_DB: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed capture last seen');
            },
        },
        processor: '/get_all_users_last_seen_for_copy_to_db',
    },
    WIFY_COPY_LAST_SEEN_FROM_REDIS_TO_DB: {
        ons: {
            completed: (job, job_result) => {
                // console.log('Processed copy last seen frm redis to db');
            },
        },
        processor: '/copy_last_seen_frm_redis_to_db',
    },
    WIFY_AUTO_ASSIGN_AUTORITY_GET_SRVC_REQ_OF_SRVC_TYPE: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed auto assign authority');
            },
        },
        processor: '/auto_assign_authority_by_loc',
    },
    WIFY_AUTO_ASSIGN_AUTHORITY_ON_EXISTING_SRVC_REQS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed auto assign authority');
            },
        },
        processor: '/auto_assign_authority_by_loc_fr_existing_req',
    },
    WIFY_NEW_SBTSK_CREATION_NOTIFICATION: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed auto assign authority');
            },
        },
        processor: '/send_new_sbtsk_creation_notification',
    },
    WIFY_TMS_PROCESS_DUE_SUTBASK_LIST_BY_SBTSK_TYPE: {
        ons: {
            completed: (job, job_result) => {
                //  console.log('Processed due sbtsk list');
            },
        },
        processor: '/process_due_sbtsk_list_by_sbtsk_type',
    },
    WIFY_TMS_PROCESS_GET_UPCOMING_SUBTASK_LIST_FOR_REMINDER_BY_SBTSK_ID: {
        ons: {
            completed: (job, job_result) => {
                //console.log('Processed due sbtsk list');
            },
        },
        processor: '/process_upcoming_task_list_by_stbsk_type',
    },
    WIFY_TMS_ADD_NOTIFICATION_LOG: {
        ons: {
            completed: (job, job_result) => {
                //  console.log('Processed due sbtsk list');
            },
        },
        processor: '/add_notification_log',
    },
    WIFY_TMS_PROACTIVE_QUEUE: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed proactive queue');
            },
        },
        processor: '/proactive/proactive',
    },
    WIFY_TMS_PROACTIVE_QUEUE_READ_STREAM: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed proactive queue read stream');
            },
        },
        processor: '/proactive/proactiveReadStreamForFile',
    },
    WIFY_TMS_PROACTIVE_QUEUE_RULE_RUNNER: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed proactive queue rule runner');
            },
        },
        processor: '/proactive/proactiveRuleRunner',
    },
    WIFY_PROJECTS_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export projects by email');
            },
        },
        processor: '/projects_export_by_email',
    },
    WIFY_AA_LAMBDA_BASED_SHEET_GENERATION: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed WIFY_AA_LAMBDA_BASED_SHEET_GENERATION');
            },
        },
        processor: '/aa_lambda_based_sheet_generation',
    },
    WIFY_TASK_UPDATES_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export task updates by email');
            },
        },
        processor: '/task_updates_exports_by_email',
    },
    WIFY_SBTSK_DELETION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed sbtsk deletion workflow');
            },
        },
        processor: '/process_sbtsk_deletion',
    },
    WIFY_SBTSK_ASSIGNMENT_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed sbtsk assignment workflow');
            },
        },
        processor: '/process_sbtsk_technican_assignment',
    },
    WIFY_SBTSK_REASSIGNMENT_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed sbtsk reassignment workflow');
            },
        },
        processor: '/process_sbtsk_reassignment',
    },
    WIFY_SRVC_REQ_DELETION: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed srvc req deletion workflow');
            },
        },
        processor: '/process_srvc_req_deletion',
    },
    WIFY_UNALLOCATED_PINCODE_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {},
        },
        processor: '/unallocated_pincode_export_by_email',
    },
    WIFY_PINCODES_NOT_IN_HUBS_EXPORT: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export pincodes not in hubs');
            },
        },
        processor: '/process_pincodes_not_in_hubs_export',
    },
    WIFY_ROLE_UPDATE_WRKFLW: {
        ons: {
            completed: (job, job_result) => {},
        },
        processor: '/role_update_workflow',
    },
    WIFY_SRVC_TYPE_UPDATE_WRKFLW: {
        ons: {
            completed: (job, job_result) => {},
        },
        processor: '/srvc_type_update_workflow',
    },
    WIFY_UPDATE_VERTICAL_FOR_SRVC_REQS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed update vertical for service requests');
            },
        },
        processor: '/update_vertical_for_srvc_reqs',
    },
    WIFY_PROCESS_RATINGS_QUEUE: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed ratings queue');
            },
        },
        processor: '/process_ratings_queue',
    },
    WIFY_GAI_RATING_FOR_TECHNICIAN_SUBTASK: {
        ons: {
            completed: (job, job_result) => {},
        },
        processor: '/gai_rating_for_technician_subtask',
    },
    WIFY_PROCESS_AUTO_PUNCH_OUT: {
        ons: {
            completed: (job, job_result) => {},
        },
        processor: '/process_auto_punch_out',
    },
    WIFY_RATINGS_EXPORT_BY_EMAIL: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed export ratings by email');
            },
        },
        processor: '/ratings_export_by_email',
    },
    WIFY_TMS_PROCESS_TIME_BASED_NOTIFICATION: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed time based notification');
            },
        },
        processor: '/process_time_based_config',
    },
    WIFY_TMS_PROCESS_TIME_BASED_SINGLE_NOTIFICATION_CONFIG: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed time based notification');
            },
        },
        processor: '/process_time_based_single_notification_config',
    },
    WIFY_TMS_PROCESS_READ_TIME_BASED_NOTIFICATION_REMINDERS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed time based notification');
            },
        },
        processor: '/process_read_time_based_notification_reminders',
    },
    WIFY_TMS_PROCESS_EMAIL_TIME_BASED_EMAIL_CONFIG: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed time based notification');
            },
        },
        processor: '/process_time_based_email_config',
    },
    WIFY_TMS_PROCESS_HASHING_USING_BCRYPT: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed hashing using bcrypt completed.');
            },
        },
        processor: '/process_hashing_using_bcrypt',
    },
    CRON_SRVC_TYPE_PERIODIC_AUTOMATIONS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed CRON_SRVC_TYPE_PERIODIC_AUTOMATIONS');
            },
        },
        processor: '/process_srvc_type_periodic_automation',
    },
    CRON_CAPACITY_UPDATE: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    `org_${job.data.org_id || 'unknown'} | QUEUE: completed`
                );
            },
        },
        processor: '/process_capacity_update',
    },
    CRON_CAPACITY_BATCH_UPDATE: {
        ons: {
            completed: (job, job_result) => {
                // Extract resourceId from the first record in the batch if available
                const resourceId =
                    job.data.transformedBatch &&
                    job.data.transformedBatch.length > 0
                        ? job.data.transformedBatch[0].resourceId
                        : `org_${job.data.orgId || 'unknown'}`;
                console.log(
                    `${resourceId} | BATCH: ${job.data.batchIndex + 1}/${job.data.totalBatches} | STATUS: completed`
                );
            },
            failed: (job, err) => {
                // Extract resourceId from the first record in the batch if available
                const resourceId =
                    job.data.transformedBatch &&
                    job.data.transformedBatch.length > 0
                        ? job.data.transformedBatch[0].resourceId
                        : `org_${job.data.orgId || 'unknown'}`;
                console.error(
                    `${resourceId} | BATCH: ${job.data.batchIndex + 1}/${job.data.totalBatches} | STATUS: failed | ERROR: ${err.message || 'unknown'}`
                );
            },
        },
        processor: '/process_capacity_batch_update',
    },
    CRON_CAPACITY_RESOURCE_UPDATE: {
        ons: {
            completed: (job, job_result) => {
                console.log(
                    `${job.data.resourceId || 'unknown'} | QUEUE: completed`
                );
            },
            failed: (job, err) => {
                console.error(
                    `${job.data.resourceId || 'unknown'} | QUEUE: failed | ERROR: ${err.message || 'unknown'}`
                );
            },
        },
        processor: '/process_capacity_resource_update',
    },
    WIFY_TMS_PROCESS_GAI_AUDIO_TRANSCRIPT: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed CRON_SRVC_TYPE_PERIODIC_AUTOMATIONS');
            },
        },
        processor: '/process_gai_audio_transcript',
    },
    WIFY_TMS_PROCESS_SRVC_REQ_DELETION_NOTIFICATION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed srvc req notification workflow');
            },
        },
        processor: '/process_srvc_req_deletion_notification_workflow',
    },
    WIFY_TMS_PROCESS_SUBTASK_DELETION_NOTIFICATION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed sbtsk deletion notification workflow');
            },
        },
        processor: '/process_subtask_deletion_notification_workflow',
    },
    WIFY_TMS_SUBTASK_DELETION_NOTIFICATION: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed subtask deletion notification');
            },
        },
        processor: '/subtask_deletion_notification',
    },
    WIFY_TMS_PROCESS_SUBTASK_REASSIGN_NOTIFICATION_WORKFLOW: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed sbtsk reassign notification workflow');
            },
        },
        processor: '/process_subtask_reassign_notification_workflow',
    },
    WIFY_SBTSK_REASSIGN_NOTIFICATION: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed subtask reassign notification');
            },
        },
        processor: '/send_subtask_reassign_notification',
    },
    WIFY_INTERAL_OPERATIONS: {
        ons: {
            completed: (job, job_result) => {
                console.log('Processed internal operations queue');
            },
        },
        processor: '/process_internal_operations',
    },
};

// adding addJob function

Object.keys(allQueues).map((singleQueuekey) => {
    allQueues[singleQueuekey].addJob = (job_data, options = {}) => {
        const app = require('../../app');
        app.get(`Queue_${singleQueuekey}_ADD`)(job_data, options);
    };
    if (singleQueuekey.startsWith('CRON_')) {
        allQueues[singleQueuekey].deleteJobByPrefix = async (jobIdPrefix) => {
            const app = require('../../app');
            const queue = app.get(`Queue_${singleQueuekey}`);
            try {
                // Retrieve all repeatable jobs
                const repeatableJobs = await queue.getRepeatableJobs();

                // Find jobs with matching jobIdPrefix prefix
                const jobsToRemove = repeatableJobs.filter((job) =>
                    job.id.startsWith(jobIdPrefix)
                );

                if (jobsToRemove.length > 0) {
                    for (const job of jobsToRemove) {
                        // Remove the repeatable job using its key
                        await queue.removeRepeatableByKey(job.key);
                        console.log(
                            `${singleQueuekey} Removed repeatable job with jobIdPrefix: ${jobIdPrefix} key: ${job.id}`
                        );
                    }
                    return true;
                } else {
                    console.log(
                        `${singleQueuekey} No repeatable job found with jobIdPrefix: ${jobIdPrefix}`
                    );
                }
            } catch (err) {
                console.error(
                    `${singleQueuekey} Error removing repeatable jobs:`,
                    err
                );
            }
        };
    }
});

exports.allQueues = allQueues;
