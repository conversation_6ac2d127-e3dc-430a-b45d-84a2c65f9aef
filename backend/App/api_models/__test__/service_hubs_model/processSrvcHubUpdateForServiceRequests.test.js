const service_hubs_model = require('../../setup/capacity/service_hubs_model');
const {
    setupModelWithTestPIData,
    mockDbFn,
} = require('../../utils/test_utils');

// Mock the queues module
jest.mock('../../queues_v2/queues', () => ({
    allQueues: {
        WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS: {
            addJob: jest.fn(),
        },
    },
}));

setupModelWithTestPIData(service_hubs_model);

const prefix = 'ServiceHubsModel :: processSrvcHubUpdateForServiceRequests ::';

beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
    mockDbFn(service_hubs_model, 'tms_hlpr_assign_new_hub_to_srvc_reqs', {});
    mockDbFn(service_hubs_model, 'tms_hlpr_update_existing_hub_for_srvc_reqs', {});
});

beforeEach(() => {
    jest.clearAllMocks();
});

afterAll(() => {
    console.error.mockRestore();
    console.log.mockRestore();
});

test(`${prefix} smoke test`, () => {
    expect(service_hubs_model).toBeInstanceOf(Object);
});

test(`${prefix} processSrvcHubUpdateForServiceRequests function exists`, () => {
    expect(service_hubs_model.processSrvcHubUpdateForServiceRequests).toBeInstanceOf(Function);
});

test(`${prefix} should skip processing when no entry_id in responseData`, () => {
    const { allQueues } = require('../../queues_v2/queues');
    
    const query = {
        vertical_id: 1,
        pincodes: ['123456'],
        is_active: true
    };
    const responseData = {}; // No entry_id

    service_hubs_model.processSrvcHubUpdateForServiceRequests(query, responseData);

    expect(allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob).not.toHaveBeenCalled();
});

test(`${prefix} should process new hub creation`, () => {
    const { allQueues } = require('../../queues_v2/queues');
    
    const query = {
        vertical_id: 1,
        pincodes: ['123456', '789012'],
        is_active: true
        // No entry_id means new hub
    };
    const responseData = {
        entry_id: 42
    };

    service_hubs_model.processSrvcHubUpdateForServiceRequests(query, responseData);

    expect(allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob).toHaveBeenCalledWith({
        query: {
            org_id: 123, // from test utils
            usr_id: '1b5b0d19-f59c-4fef-9aa5-5771a195851f', // from test utils
            vertical_id: 1,
            hub_id: 42,
            pincodes: ['123456', '789012'],
            ip_address: 'test-ip',
            user_agent: 'jest-agent',
        },
        operation: 'assign_new_hub',
        service_hubs_model_data: {
            ip_address: 'test-ip',
            user_agent: 'jest-agent',
            userContext: service_hubs_model.userContext,
        },
    });
});

test(`${prefix} should process existing hub update`, () => {
    const { allQueues } = require('../../queues_v2/queues');
    
    const query = {
        entry_id: 42, // Existing hub
        vertical_id: 1,
        pincodes: ['123456', '789012'],
        is_active: false
    };
    const responseData = {
        entry_id: 42
    };

    service_hubs_model.processSrvcHubUpdateForServiceRequests(query, responseData);

    expect(allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob).toHaveBeenCalledWith({
        query: {
            org_id: 123, // from test utils
            usr_id: '1b5b0d19-f59c-4fef-9aa5-5771a195851f', // from test utils
            vertical_id: 1,
            hub_id: 42,
            pincodes: ['123456', '789012'],
            is_active: false,
            ip_address: 'test-ip',
            user_agent: 'jest-agent',
        },
        operation: 'update_existing_hub',
        service_hubs_model_data: {
            ip_address: 'test-ip',
            user_agent: 'jest-agent',
            userContext: service_hubs_model.userContext,
        },
    });
});

test(`${prefix} assignNewHubToServiceRequests function exists`, () => {
    expect(service_hubs_model.assignNewHubToServiceRequests).toBeInstanceOf(Function);
});

test(`${prefix} updateExistingHubForServiceRequests function exists`, () => {
    expect(service_hubs_model.updateExistingHubForServiceRequests).toBeInstanceOf(Function);
});

test(`${prefix} getServiceHubsModelData function exists`, () => {
    expect(service_hubs_model.getServiceHubsModelData).toBeInstanceOf(Function);
});

test(`${prefix} getServiceHubsModelData returns correct structure`, () => {
    const mockModel = {
        ip_address: 'test-ip',
        user_agent_: 'test-agent',
        userContext: { test: 'context' }
    };

    const result = service_hubs_model.getServiceHubsModelData(mockModel);

    expect(result).toEqual({
        ip_address: 'test-ip',
        user_agent: 'test-agent',
        userContext: { test: 'context' }
    });
});
