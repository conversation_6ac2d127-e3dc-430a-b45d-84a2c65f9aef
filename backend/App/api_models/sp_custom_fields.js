var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('./utils/db_resp');
const users_model = require('./users_model');
const pagination_filters_utils = require('./utils/pagination_filters_utils');

class sp_custom_fields {
    getSingleEntry(query, entry_id) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_sp_custom_fields_details(form_data, entry_id).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }
                    var dbResp = new db_resp(
                        res[0].tms_get_sp_custom_fields_details
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        this.getViewDataFrSpCustomFieldsForm({ entry_id }).then(
                            (operationResp) => {
                                if (operationResp.isSuccess()) {
                                    var finalResp = JSON.parse(
                                        operationResp.resp
                                    );
                                    // console.log(finalResp);
                                    finalResp.form_data = dbResp.data;
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            JSON.stringify(finalResp),
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                } else {
                                    resolve(operationResp);
                                }
                            }
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAll(form) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(form);
            var org_id = users_model.getOrgId(this.userContext);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_sp_custom_fields(
                    org_id,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }
                        var dbResp = new db_resp(
                            res[0].tms_get_sp_custom_fields
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getViewDataFrSpCustomFieldsForm(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_sp_custom_fields_getview_data(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_sp_custom_fields_getview_data
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdateSpCustomFields(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);
            if (!this.validateCustForm(form_data)) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Please fill mandatory * field',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_create_sp_custom_fields(form_data, entry_id).then(
                (resp) => {
                    var customFieldResp = new db_resp(
                        resp[0].tms_create_sp_custom_fields
                    );

                    if (!customFieldResp.status) {
                        if (customFieldResp.code == 'vertical_name_exist') {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Vertical name already exists',
                                    HttpStatus.StatusCodes.BAD_REQUEST
                                )
                            );
                        } else if (
                            customFieldResp.code ==
                            'service_type_and_vertical_nature_are_different'
                        ) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    JSON.stringify(
                                        customFieldResp.data.error_message
                                    ),
                                    HttpStatus.StatusCodes.BAD_REQUEST
                                )
                            );
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                        }
                        return;
                    } else {
                        // Check if the srvc_type_id exists in the form_data
                        // if yes loop on all the srvc_type_id and
                        // add a job to update the vertical_id of all service requests that belong to that srvc_type_id
                        // Then add another one job to remove the vertical_id from service requests for
                        // which srvc_type_id is not in the above service type ids but have this vertical_id

                        // Process vertical_id updates for service requests
                        this.processVerticalUpdatesForServiceRequests(
                            query,
                            customFieldResp.data
                        );

                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(customFieldResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    fatalDbError(resolve, error) {
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    validateCustForm(form_data) {
        return true;
    }

    /**
     * Process vertical_id updates for service requests
     * @param {Object} query - The query object containing form data
     * @param {Object} responseData - The response data from the database
     */
    processVerticalUpdatesForServiceRequests(query, responseData) {
        // Check if the srvc_type_id exists in the form_data
        if (
            !query ||
            !query.srvc_type_id ||
            !Array.isArray(query.srvc_type_id) ||
            query.srvc_type_id.length === 0
        ) {
            console.log(
                'sp_custom_fields::processVerticalUpdatesForServiceRequests:: No service type IDs found in the form data'
            );
            return;
        }

        try {
            const { allQueues } = require('./queues_v2/queues');
            const entry_id = responseData.db_id;
            const org_id = users_model.getOrgId(this.userContext);
            const usr_id = users_model.getUUID(this.userContext);

            // For each service type ID, add a job to update vertical_id for service requests
            query.srvc_type_id.forEach((srvc_type_id) => {
                const jobData = {
                    query: {
                        org_id,
                        usr_id,
                        srvc_type_id,
                        vertical_id: entry_id,
                        ip_address: this.ip_address,
                        user_agent: this.user_agent_,
                    },
                    operation: 'update',
                    sp_custom_fields_model_data:
                        this.getSpCustomFieldsModelData(this),
                };

                // Add job to update vertical_id for service requests with this service type
                allQueues.WIFY_UPDATE_VERTICAL_FOR_SRVC_REQS.addJob(jobData);

                console.log(
                    `sp_custom_fields::processVerticalUpdatesForServiceRequests:: Added job to update vertical_id for service type: ${srvc_type_id}`
                );
            });

            // Add job to remove vertical_id from service requests that don't belong to these service types
            // but have this vertical_id
            const jobData = {
                query: {
                    org_id,
                    usr_id,
                    srvc_type_ids_to_exclude: query.srvc_type_id,
                    vertical_id: entry_id,
                    ip_address: this.ip_address,
                    user_agent: this.user_agent_,
                },
                operation: 'remove',
                sp_custom_fields_model_data:
                    this.getSpCustomFieldsModelData(this),
            };

            allQueues.WIFY_UPDATE_VERTICAL_FOR_SRVC_REQS.addJob(jobData);

            console.log(
                `sp_custom_fields::processVerticalUpdatesForServiceRequests:: Added job to remove vertical_id for service types not in: ${query.srvc_type_id.join(', ')}`
            );
        } catch (error) {
            console.error(
                'sp_custom_fields::processVerticalUpdatesForServiceRequests:: Error processing vertical updates for service requests:',
                error
            );
        }
    }

    /**
     * Get model data for queue
     * @returns {Object} Model data for queue
     */
    getSpCustomFieldsModelData(model) {
        return {
            ip_address: model.ip_address,
            user_agent: model.user_agent_,
            userContext: model.userContext,
        };
    }

    /**
     * Update vertical_id for service requests with a specific service type
     * @param {Object} query - The query object containing form data
     */
    async updateVerticalForServiceRequests(query) {
        try {
            console.log(
                'sp_custom_fields::updateVerticalForServiceRequests::',
                query
            );

            const result =
                await this.db.tms_hlpr_update_srvc_reqs_vertical_by_srvc_type(
                    JSON.stringify(query)
                );

            if (
                result &&
                result[0] &&
                result[0].tms_hlpr_update_srvc_reqs_vertical_by_srvc_type
            ) {
                const dbResp =
                    result[0].tms_hlpr_update_srvc_reqs_vertical_by_srvc_type;
                if (dbResp.status) {
                    console.log(
                        `sp_custom_fields::updateVerticalForServiceRequests:: Successfully updated vertical_id for ${dbResp.data.updated_count} service requests`
                    );
                } else {
                    console.log(
                        `sp_custom_fields::updateVerticalForServiceRequests:: Failed to update vertical_id for service requests: ${dbResp.message}`
                    );
                }
            }
        } catch (error) {
            console.error(
                'sp_custom_fields::updateVerticalForServiceRequests:: Error:',
                error
            );
        }
    }

    /**
     * Remove vertical_id from service requests that don't belong to specified service types
     * @param {Object} query - The query object containing form data
     */
    async removeVerticalFromServiceRequests(query) {
        try {
            console.log(
                'sp_custom_fields::removeVerticalFromServiceRequests::',
                query
            );

            const result =
                await this.db.tms_hlpr_remove_vertical_from_srvc_reqs(
                    JSON.stringify(query)
                );

            if (
                result &&
                result[0] &&
                result[0].tms_hlpr_remove_vertical_from_srvc_reqs
            ) {
                const dbResp =
                    result[0].tms_hlpr_remove_vertical_from_srvc_reqs;
                if (dbResp.status) {
                    console.log(
                        `sp_custom_fields::removeVerticalFromServiceRequests:: Successfully removed vertical_id from ${dbResp.data.updated_count} service requests`
                    );
                } else {
                    console.log(
                        `sp_custom_fields::removeVerticalFromServiceRequests:: Failed to remove vertical_id from service requests: ${dbResp.message}`
                    );
                }
            }
        } catch (error) {
            console.error(
                'sp_custom_fields::removeVerticalFromServiceRequests:: Error:',
                error
            );
        }
    }

    /**
     * Set the IP address
     * @param {string} ip_address - The IP address
     */
    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }

    /**
     * Set the user agent
     * @param {string} user_agent_ - The user agent string
     */
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
    getFreshInstance(model) {
        const clonedInstance = new sp_custom_fields();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new sp_custom_fields();
        return instance;
    }
}

module.exports = new sp_custom_fields();
