var sampleOperationResp = require('../../utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../../utils/db_resp');
const users_model = require('../../users_model');
const geoCodingUtils = require('../../utils/geo_coding_utils');
class service_hubs_model {
    getSingleEntryData(query, entry_id) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = JSON.stringify(query);
                let respData = (
                    await this.db.tms_hub_getview_data(form_data, entry_id)
                )[0].tms_hub_getview_data;

                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getLocationInfo(query, pincodes) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = query;

                let updatedPincodes = pincodes.map(
                    (pincode) => pincode + '+India'
                );
                form_data['pincodes'] = updatedPincodes;
                var temp_form_data = form_data;
                form_data = JSON.stringify(form_data);
                // console.log('getLocationInfo form_data send => ', form_data);

                // get exsting pincodes location details
                const dbResp = (
                    await this.db.tms_get_pincodes_geo_loc_from_cache(form_data)
                )[0].tms_get_pincodes_geo_loc_from_cache;

                // console.log('getLocationInfo form_data respData => ', dbResp);

                let locationDetails = [];
                let foundPincodes = new Set();

                if (dbResp && dbResp.status && dbResp.data) {
                    locationDetails = dbResp.data;
                    foundPincodes = new Set(
                        locationDetails.map((loc) => loc.address.split('+')[0])
                    ); // Extracting pincode from address
                }

                // Identify missing pincodes get loc details & save into db
                let missingPincodes = pincodes.filter(
                    (pincode) => !foundPincodes.has(pincode)
                );
                if (missingPincodes.length > 0) {
                    // Fetch locations for missing pincodes
                    let newLocationData =
                        await geoCodingUtils.fetchAndStorePincodeBoundaries(
                            missingPincodes
                        );
                    if (newLocationData) {
                        temp_form_data['location_data'] = newLocationData;
                        temp_form_data = JSON.stringify(temp_form_data);
                        let storeResp = (
                            await this.db.tms_add_geo_coding_cache_batch(
                                temp_form_data
                            )
                        )[0].tms_add_geo_coding_cache_batch;
                        if (storeResp.status) {
                            // Merge API-fetched data with existing DB data & send complete list
                            locationDetails = [
                                ...locationDetails,
                                ...newLocationData,
                            ];
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(locationDetails),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp?.message || 'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                        }
                    } else {
                        resolve(
                            new sampleOperationResp(
                                false,
                                `No location details for ${missingPincodes.join(', ')} pincodes`,
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    }
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                console.error('service_hubs_model::getLocationInfo', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    createOrUpdateHub(query, entry_id = 0) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = JSON.stringify(query);
                let respData = (
                    await this.db.tms_create_or_update_srvc_hub(
                        form_data,
                        entry_id
                    )
                )[0].tms_create_or_update_srvc_hub;

                if (respData.code == 'hub_name_or_code_already_exists') {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Hub name or code already exists',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                } else if (
                    respData.code == 'pincode_already_exists_in_another_hub'
                ) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Pincode already exists in another service hub',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                } else if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Internal server Error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );

                    return;
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.error('service_hubs_model::createOrUpdateHub', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getAllHubs(form, vertical_id) {
        return new Promise(async (resolve, reject) => {
            try {
                if (Object.keys(form).length != 0) {
                    var org_id = users_model.getOrgId(this.userContext);
                    var pagination = JSON.parse(form.pagination);
                    var page_no = pagination.current;
                    var page_size = pagination.pageSize;
                    var search_query = form.search_query;
                    //Filters remove -1 in from array
                    var filters = JSON.parse(form.filters);
                    var filtersAllKeys = Object.keys(filters);
                    for (var i = 0; i < filtersAllKeys.length; i++) {
                        if (
                            filters[filtersAllKeys[i]]?.length > 0 &&
                            Array.isArray(filters[filtersAllKeys[i]]) &&
                            filters[filtersAllKeys[i]].includes('-1')
                        ) {
                            delete filters[filtersAllKeys[i]];
                        }
                    }
                    filters = JSON.stringify(filters);
                }
                const dbResp = (
                    await this.db.tms_get_srvc_hubs(
                        org_id,
                        vertical_id,
                        page_no,
                        page_size,
                        filters,
                        search_query
                    )
                )[0].tms_get_srvc_hubs;
                // console.log('dbResp tms_get_srvc_hubs => dbResp');
                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            dbResp?.message || 'Internal server Error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getViewDataFrHubForm(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                let respData = (
                    await this.db.tms_hub_getview_data(JSON.stringify(query))
                )[0].tms_hub_getview_data;
                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Unable to load',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.error('service_hubs_model::getProto', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Unable to load',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getOverviewProto(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                let respData = (
                    await this.db.tms_get_service_hubs_overview_proto(
                        JSON.stringify(query)
                    )
                )[0].tms_get_service_hubs_overview_proto;
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(respData.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('service_hubs_model::getProto', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Unable to load',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new service_hubs_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new service_hubs_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    /**
     * Process service hub updates for service requests
     * This function will be called after a service hub is created or updated
     * to handle any necessary updates to related service requests
     * @param {Object} query - The query object containing form data
     * @param {Object} responseData - The response data from hub creation/update
     */
    processSrvcHubUpdateForServiceRequests(query, responseData) {
        try {
            console.log(
                'service_hubs_model::processSrvcHubUpdateForServiceRequests:: Starting processing for service hub updates'
            );

            // TODO: Implementation will be added later
            // This function will handle:
            // - Updating service requests when hub pincodes change
            // - Reassigning service requests to new hubs
            // - Updating location mappings for affected service requests
            // - Processing capacity updates for affected resources

            console.log(
                'service_hubs_model::processSrvcHubUpdateForServiceRequests:: Processing completed (placeholder)'
            );
        } catch (error) {
            console.error(
                'service_hubs_model::processSrvcHubUpdateForServiceRequests:: Error processing service hub updates for service requests:',
                error
            );
        }
    }
}

module.exports = new service_hubs_model();
