var sampleOperationResp = require('../../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../../api_models/utils/db_resp');
const pagination_filters_utils = require('../utils/pagination_filters_utils');
const users_model = require('../users_model');
const service_type_workflow = require('../workflows/service_type_workflow');
const {
    generateErrorResponse,
    log,
    levels,
} = require('../utils/errors/helper');
const API_ERROR_CODES = require('../utils/errors/apiErrorConstant');

class srvc_types_model {
    getFreshInstance(model) {
        const clonedInstance = new srvc_types_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getModelData(model, getUserAndOrgIdFrQuery = false) {
        let userAndOrgIdFrQuery = {};
        if (getUserAndOrgIdFrQuery) {
            userAndOrgIdFrQuery = {
                org_id: users_model.getOrgId(this.userContext),
                usr_id: users_model.getUUID(this.userContext),
            };
        }
        return {
            ip_address: model.ip_address,
            user_agent: model.user_agent_,
            user_context: model.user_context,
            ...userAndOrgIdFrQuery,
        };
    }

    getSingleEntry(query, entry_id) {
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         JSON.stringify({}),
            //         HttpStatus.StatusCodes.OK)
            // );
            // return;
            var org_id = users_model.getOrgId(this.userContext);

            if (!this.db) {
                const error = new Error(API_ERROR_CODES.TMS_API_0X001.message);
                log({
                    level: levels.error,
                    message: API_ERROR_CODES.TMS_API_0X001.message,
                    errorCode:
                        API_ERROR_CODES.TMS_API_0X001.subCodes.TMS_API_0X001_001
                            .key,
                    error: error.message,
                    stack: error.stack,
                });
                resolve(
                    new sampleOperationResp(
                        false,
                        generateErrorResponse({
                            errorCode: API_ERROR_CODES.TMS_API_0X001.key,
                            subCodeKey:
                                API_ERROR_CODES.TMS_API_0X001.subCodes
                                    .TMS_API_0X001_001.key,
                            error: error,
                        }),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_srvc_type_details(org_id, entry_id).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                generateErrorResponse({
                                    errorCode:
                                        API_ERROR_CODES.TMS_API_0X004.key,
                                    subCodeKey:
                                        API_ERROR_CODES.TMS_API_0X004.subCodes
                                            .TMS_API_0X004_001.key,
                                }),
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_srvc_type_details);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                generateErrorResponse({
                                    errorCode:
                                        API_ERROR_CODES.TMS_API_0X004.key,
                                    subCodeKey:
                                        API_ERROR_CODES.TMS_API_0X004.subCodes
                                            .TMS_API_0X004_002.key,
                                    // error: error,
                                }),
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        this.getViewDataFrForm({}).then((operationResp) => {
                            // console.log(dbResp.data);
                            if (operationResp.isSuccess()) {
                                var finalResp = JSON.parse(operationResp.resp);
                                // console.log(dbResp.data);
                                // decode statuses
                                var finalStatuses = {};
                                dbResp.data.srvc_statuses_db.forEach(
                                    (element) => {
                                        // console.log("here");
                                        var status_type = element.status_type;
                                        if (
                                            finalStatuses[status_type] ==
                                            undefined
                                        ) {
                                            finalStatuses[status_type] = [];
                                        }
                                        delete element[status_type];
                                        finalStatuses[status_type].push(
                                            element
                                        );
                                    }
                                );
                                dbResp.data.srvc_statuses =
                                    JSON.stringify(finalStatuses);
                                // end decoding statuses
                                finalResp.form_data = {
                                    ...dbResp.data.form_data,
                                    ...dbResp.data,
                                };
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(finalResp),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else {
                                resolve(operationResp);
                            }
                        });
                    }
                },
                (error) => {
                    log({
                        level: levels.error,
                        message: API_ERROR_CODES.TMS_API_0X005.message,
                        errorCode:
                            API_ERROR_CODES.TMS_API_0X005.subCodes
                                .TMS_API_0X005_001.key,
                        error: error.message,
                        stack: error.stack,
                    });
                    resolve(
                        new sampleOperationResp(
                            false,
                            generateErrorResponse({
                                errorCode: API_ERROR_CODES.TMS_API_0X005.key,
                                subCodeKey:
                                    API_ERROR_CODES.TMS_API_0X005.subCodes
                                        .TMS_API_0X005_001.key,
                                error: error,
                            }),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                }
            );
        });
    }

    createOrUpdate(query, entry_id = 0) {
        // console.log("Query rxd : ",JSON.stringify(query));
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         "Got Data - " + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            // console.log("form_data",form_data);
            var validationResp = this.validateCreateNewForm(query);
            if (!validationResp.isSuccess()) {
                resolve(validationResp);
                return;
            }
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            // resolve(
            //     new sampleOperationResp(false,
            //         "Got Data - " + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                log({
                    level: levels.error,
                    message: API_ERROR_CODES.TMS_API_0X001.message,
                    errorCode:
                        API_ERROR_CODES.TMS_API_0X001.subCodes.TMS_API_0X001_002
                            .key,
                });
                resolve(
                    new sampleOperationResp(
                        false,
                        generateErrorResponse({
                            errorCode: API_ERROR_CODES.TMS_API_0X001.key,
                            subCodeKey:
                                API_ERROR_CODES.TMS_API_0X001.subCodes
                                    .TMS_API_0X001_002.key,
                            //error: error,
                        }),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_create_srvc_type(form_data, entry_id).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_create_srvc_type);

                    if (dbResp.code == 'title_or_key_exists') {
                        log({
                            level: levels.error,
                            message: API_ERROR_CODES.TMS_API_0X003.message,
                            errorCode:
                                API_ERROR_CODES.TMS_API_0X003.subCodes
                                    .TMS_API_0X003_001.key,
                        });
                        resolve(
                            new sampleOperationResp(
                                false,
                                generateErrorResponse({
                                    errorCode:
                                        API_ERROR_CODES.TMS_API_0X003.key,
                                    subCodeKey:
                                        API_ERROR_CODES.TMS_API_0X003.subCodes
                                            .TMS_API_0X003_001.key,
                                }),
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (!dbResp.status) {
                        log({
                            level: levels.error,
                            message: API_ERROR_CODES.TMS_API_0X004.message,
                            errorCode:
                                API_ERROR_CODES.TMS_API_0X004.subCodes
                                    .TMS_API_0X004_003.key,
                        });
                        resolve(
                            new sampleOperationResp(
                                false,
                                generateErrorResponse({
                                    errorCode:
                                        API_ERROR_CODES.TMS_API_0X004.key,
                                    subCodeKey:
                                        API_ERROR_CODES.TMS_API_0X004.subCodes
                                            .TMS_API_0X004_003.key,
                                    // error: error,
                                }),
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        let workflow = new service_type_workflow(this);
                        workflow.trigger(query, entry_id);
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    log({
                        level: levels.error,
                        message: API_ERROR_CODES.TMS_API_0X005.message,
                        errorCode:
                            API_ERROR_CODES.TMS_API_0X005.subCodes
                                .TMS_API_0X005_002.key,
                        error: error.message,
                        stack: error.stack,
                    });
                    resolve(
                        new sampleOperationResp(
                            false,
                            generateErrorResponse({
                                errorCode: API_ERROR_CODES.TMS_API_0X005.key,
                                subCodeKey:
                                    API_ERROR_CODES.TMS_API_0X005.subCodes
                                        .TMS_API_0X005_002.key,
                                error: error,
                            }),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                }
            );
        });
    }

    getViewDataFrForm(query) {
        // console.log("Query rxd : ",query);
        return new Promise((resolve, reject) => {
            // missing parameter or validation failed response
            // resolve(
            //     new sampleOperationResp(false,
            //         "bla bla field missing!",
            //         HttpStatus.StatusCodes.BAD_REQUEST)
            // );
            // submit failed due to internal error
            // resolve(
            //     new sampleOperationResp(false,
            //         "Internal server error please try again!",
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                log({
                    level: levels.error,
                    message: API_ERROR_CODES.TMS_API_0X001.message,
                    errorCode:
                        API_ERROR_CODES.TMS_API_0X001.subCodes.TMS_API_0X001_003
                            .key,
                });
                resolve(
                    new sampleOperationResp(
                        false,
                        generateErrorResponse({
                            errorCode: API_ERROR_CODES.TMS_API_0X001.key,
                            subCodeKey:
                                API_ERROR_CODES.TMS_API_0X001.subCodes
                                    .TMS_API_0X001_003.key,
                            // error: error,
                        }),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_view_data_srvc_type(form_data).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_view_data_srvc_type);

                    if (!dbResp.status) {
                        log({
                            level: levels.error,
                            message: API_ERROR_CODES.TMS_API_0X004.message,
                            errorCode:
                                API_ERROR_CODES.TMS_API_0X004.subCodes
                                    .TMS_API_0X004_004.key,
                        });
                        resolve(
                            new sampleOperationResp(
                                false,
                                generateErrorResponse({
                                    errorCode:
                                        API_ERROR_CODES.TMS_API_0X004.key,
                                    subCodeKey:
                                        API_ERROR_CODES.TMS_API_0X004.subCodes
                                            .TMS_API_0X004_004.key,
                                    //  error: error,
                                }),
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    log({
                        level: levels.error,
                        message: API_ERROR_CODES.TMS_API_0X005.message,
                        errorCode:
                            API_ERROR_CODES.TMS_API_0X005.subCodes
                                .TMS_API_0X005_003.key,
                        error: error.message,
                        stack: error.stack,
                    });
                    resolve(
                        new sampleOperationResp(
                            false,
                            generateErrorResponse({
                                errorCode: API_ERROR_CODES.TMS_API_0X005.key,
                                subCodeKey:
                                    API_ERROR_CODES.TMS_API_0X005.subCodes
                                        .TMS_API_0X005_003.key,
                                error: error,
                            }),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                }
            );
            return;
        });
    }

    getAll(form) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(form);
            var org_id = users_model.getOrgId(this.userContext);
            if (!this.db) {
                log({
                    level: levels.error,
                    message: API_ERROR_CODES.TMS_API_0X001.message,
                    errorCode:
                        API_ERROR_CODES.TMS_API_0X001.subCodes.TMS_API_0X001_004
                            .key,
                });
                resolve(
                    new sampleOperationResp(
                        false,
                        generateErrorResponse({
                            errorCode: API_ERROR_CODES.TMS_API_0X001.key,
                            subCodeKey:
                                API_ERROR_CODES.TMS_API_0X001.subCodes
                                    .TMS_API_0X001_004.key,
                            // error: error,
                        }),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_srvc_types(
                    org_id,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            log({
                                level: levels.error,
                                message: API_ERROR_CODES.TMS_API_0X004.message,
                                errorCode:
                                    API_ERROR_CODES.TMS_API_0X004.subCodes
                                        .TMS_API_0X004_005.key,
                            });
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    generateErrorResponse({
                                        errorCode:
                                            API_ERROR_CODES.TMS_API_0X004.key,
                                        subCodeKey:
                                            API_ERROR_CODES.TMS_API_0X004
                                                .subCodes.TMS_API_0X004_005.key,
                                        // error: error,
                                    }),
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(res[0].tms_get_srvc_types);

                        if (!dbResp.status) {
                            log({
                                level: levels.error,
                                message: API_ERROR_CODES.TMS_API_0X004.message,
                                errorCode:
                                    API_ERROR_CODES.TMS_API_0X004.subCodes
                                        .TMS_API_0X004_006.key,
                            });
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    generateErrorResponse({
                                        errorCode:
                                            API_ERROR_CODES.TMS_API_0X004.key,
                                        subCodeKey:
                                            API_ERROR_CODES.TMS_API_0X004
                                                .subCodes.TMS_API_0X004_006.key,
                                        //  error: error,
                                    }),
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        log({
                            level: levels.error,
                            message: API_ERROR_CODES.TMS_API_0X005.message,
                            errorCode:
                                API_ERROR_CODES.TMS_API_0X005.subCodes
                                    .TMS_API_0X005_004.key,
                            error: error.message,
                            stack: error.stack,
                        });
                        resolve(
                            new sampleOperationResp(
                                false,
                                generateErrorResponse({
                                    errorCode:
                                        API_ERROR_CODES.TMS_API_0X005.key,
                                    subCodeKey:
                                        API_ERROR_CODES.TMS_API_0X005.subCodes
                                            .TMS_API_0X005_004.key,
                                    error: error,
                                }),
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    }
                );
        });
    }

    validateCreateNewForm(form_data) {
        if (
            form_data['srvc_type_name'] == '' ||
            form_data['srvc_type_key'] == '' ||
            form_data['srvc_type_icon_selector'] == ''
        ) {
            return new sampleOperationResp(
                false,
                'Mandatory parameters missing!',
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
        return new sampleOperationResp(
            true,
            'Good to go!',
            HttpStatus.StatusCodes.OK
        );
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
    getFreshInstance(model) {
        const clonedInstance = new srvc_types_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new srvc_types_model();
        return instance;
    }
}

module.exports = new srvc_types_model();
