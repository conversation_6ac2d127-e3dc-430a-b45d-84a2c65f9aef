CREATE OR REPLACE FUNCTION public.tms_hlpr_update_existing_hub_for_srvc_reqs(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    vertical_id_ integer;
    hub_id_ bigint;
    pincodes_ text[];
    is_active_ boolean;
    
    -- For processing
    srvc_req_ids integer[];
    user_context_json json;
    hub_update_json json;
    hub_title_ text;

    current_pincode text;
    should_keep_hub boolean := false;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    vertical_id_ := (form_data_->>'vertical_id')::integer;
    hub_id_ := (form_data_->>'hub_id')::bigint;
    pincodes_ := array(select jsonb_array_elements_text(form_data_->'pincodes'));
    is_active_ := (form_data_->>'is_active')::boolean;
    
    -- Get user context for updates
    user_context_json = tms_get_user_context_from_data(form_data_);
    
    -- Get hub title
    select hub_name
      from public.cl_tx_vertical_srvc_hubs
     where id = hub_id_
       and org_id = org_id_
      into hub_title_;
    
    if hub_title_ is null then
        status = false;
        message = 'hub_not_found';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Get all service requests that are currently tagged to this hub
    select array_agg(db_id)
      from cl_tx_srvc_req
     where srvc_prvdr = org_id_
       and prvdr_srvc_hub = hub_id_
       and is_deleted is not true
      into srvc_req_ids;

    if srvc_req_ids is null or array_length(srvc_req_ids, 1) = 0 then
        status = true;
        message = 'no_service_requests_found';
        resp_data = json_build_object('updated_count', 0);
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Process each service request
    FOR i IN 1..array_length(srvc_req_ids, 1) LOOP
        -- Get the pincode for this service request
        select form_data->>'cust_pincode'
          from cl_tx_srvc_req
         where db_id = srvc_req_ids[i]
          into current_pincode;
            
            -- Check if this service request should still be assigned to this hub
        if is_active_ is true 
            and current_pincode is not null 
            and current_pincode != '' 
            and current_pincode = any(pincodes_) then
            should_keep_hub := true;
        else
        end if;
            
        if should_keep_hub then
            -- Keep the hub assignment, update hub title if needed
            hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(hub_id_),true);
            hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(hub_title_),true);
        else
            -- Remove the hub assignment (hub deactivated or pincode no longer matches)
            hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(0::bigint),true);
            hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(''::text),true);
        end if;
            
            -- Update the service request
        perform tms_create_service_request(hub_update_json, srvc_req_ids[i]);
    END LOOP;

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'updated_count', array_length(srvc_req_ids, 1),
        'srvc_req_ids', srvc_req_ids,
        'hub_id', hub_id_,
        'hub_title', hub_title_,
        'is_active', is_active_
    );
    
    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
