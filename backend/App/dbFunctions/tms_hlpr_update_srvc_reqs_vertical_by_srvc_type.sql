CREATE OR REPLACE FUNCTION public.tms_hlpr_update_srvc_reqs_vertical_by_srvc_type(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    srvc_type_id_ integer;
    vertical_id_ integer;
    
    -- For processing
    srvc_req_ids integer[];
    vertical_json json;
    user_context_json json;
    vertical_update_json json;
    vertical_title text;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    srvc_type_id_ := (form_data_->>'srvc_type_id')::integer;
    vertical_id_ := (form_data_->>'vertical_id')::integer;
    user_context_json = tms_get_user_context_from_data(form_data_);
    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(form_data_->>'srvc_type_id'),true);      

    -- Get vertical information
    select orgs_settings.settings_data::json, 
           orgs_settings.db_id,
           orgs_settings.settings_data->>'vertical_title'
      from cl_tx_orgs_settings as orgs_settings
     where orgs_settings.org_id = org_id_
       and orgs_settings.settings_type = 'SP_CUSTOM_FIELDS'
       and orgs_settings.db_id = vertical_id_
      into vertical_json,vertical_id_,vertical_title; 

    if vertical_json is null then
        status = false;
        message = 'no_vertical_found';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Get all service requests for this service type
    select array_agg(db_id)
      from cl_tx_srvc_req
     where srvc_type_id = srvc_type_id_
       and srvc_prvdr = org_id_
       -- and vertical_id is not our desired vertical
       and ( 
            prvdr_vertical != vertical_id_
            or prvdr_vertical is null
           )
      into srvc_req_ids;

    if srvc_req_ids is null or array_length(srvc_req_ids, 1) = 0 then
        status = true;
        message = 'no_service_requests_found';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Prepare the update JSON
    vertical_update_json = jsonb_set(user_context_json::jsonb,'{vertical_id}',to_jsonb(vertical_id_),true);
    vertical_update_json = jsonb_set(vertical_update_json::jsonb,'{vertical_title}',to_jsonb(vertical_title),true);

    -- Update all service requests
    FOR i IN 1..array_length(srvc_req_ids, 1) LOOP
        perform tms_hlpr_update_srvc_req_vertical(srvc_req_ids[i], vertical_update_json);
    END LOOP;

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'updated_count', array_length(srvc_req_ids, 1),
        'srvc_req_ids', srvc_req_ids
    );
    
    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
