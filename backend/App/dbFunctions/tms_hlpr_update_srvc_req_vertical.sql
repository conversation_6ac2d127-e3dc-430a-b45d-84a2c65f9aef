CREATE OR REPLACE FUNCTION public.tms_hlpr_update_srvc_req_vertical(srvc_req_id integer, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    srvc_type_id_ integer;
    provider_id_ integer;
    vertical_id_ integer;

    vertical_json json;
    user_context_json json;
    vertical_update_json json;
    vertical_title text;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    srvc_type_id_ := (form_data_->>'srvc_type_id')::integer;
    provider_id_ := (form_data_->>'new_prvdr')::integer; 
    user_context_json = tms_get_user_context_from_data(form_data_);
    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(form_data_->>'srvc_type_id'),true);      

    if form_data_->>'vertical_title' is not null then
        vertical_title = form_data_->>'vertical_title';
        vertical_id_ = (form_data_->>'vertical_id')::integer;
        vertical_json = '{}'::json;
    else
        select orgs_settings.settings_data::json, 
               orgs_settings.db_id,
               orgs_settings.settings_data->>'vertical_title'
          from cl_tx_orgs_settings as orgs_settings
         where orgs_settings.org_id = provider_id_
           and orgs_settings.settings_type = 'SP_CUSTOM_FIELDS'
           and srvc_type_id_ = any(array(SELECT json_array_elements_text(json_extract_path(orgs_settings.settings_data,'srvc_type_id')))::integer[])
          into vertical_json,vertical_id_,vertical_title; 
    end if;
    if vertical_json is not null then
        vertical_update_json = jsonb_set(user_context_json::jsonb,'{vertical_id}',to_jsonb(vertical_id_),true);
        vertical_update_json = jsonb_set(vertical_update_json::jsonb,'{vertical_title}',to_jsonb(vertical_title),true);
        perform tms_create_service_request(vertical_update_json,srvc_req_id);
        -- Update service hub of srvc req
        perform tms_ace_hlpr_update_srvc_req_hub(srvc_req_id, user_context_json);

        status = true;
        message = 'success';
        resp_data = '{}';
    else
        -- Prepare the update JSON to remove vertical_id 
        vertical_update_json = jsonb_set(user_context_json::jsonb,'{vertical_id}',to_jsonb(0::integer),true);
        vertical_update_json = jsonb_set(vertical_update_json::jsonb,'{vertical_title}',to_jsonb(''::text),true);
        perform tms_create_service_request(vertical_update_json,srvc_req_id);
        status = true;
        message = 'no_vertical_found';
        resp_data = '{}';
    end if;   
    
    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
