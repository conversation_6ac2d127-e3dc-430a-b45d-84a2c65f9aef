CREATE OR REPLACE FUNCTION public.tms_get_srvc_req_batch_data_by_display_code(form_data_ json, display_codes_ text[])
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		resp_data_ json;
		message text;
		status boolean;
	begin
		status = false;
		message = 'Internal_error';
	
		select jsonb_object_agg(
					srvc_req_.display_code , srvc_req_.form_data ||
               jsonb_build_object(
                   'srvc_type_history_pricing_config',
                   hlpr_tms_get_srvc_type_history_price_config(
                       (srvc_req_.form_data->>'srvc_type_his_db_id')::int
                   )
               )
				) 
		  from cl_tx_srvc_req as srvc_req_ 
		 where srvc_req_.display_code = any(display_codes_)
	      into resp_data_;
	     
	
     	if resp_data_ is not null then 
			status = true;
			message = 'success';
		else
			resp_data_ = '{}';
		end if;
		 return jsonb_build_object('status',status,'code',message,'data',resp_data_); 
	END;
$function$
;
