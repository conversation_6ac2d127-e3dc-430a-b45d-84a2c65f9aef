CREATE OR REPLACE FUNCTION public.tms_hlpr_assign_new_hub_to_srvc_reqs(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    vertical_id_ integer;
    hub_id_ bigint;
    pincodes_ text[];
    
    -- For processing
    srvc_req_ids integer[];
    user_context_json json;
    hub_update_json json;
    hub_title_ text;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    vertical_id_ := (form_data_->>'vertical_id')::integer;
    hub_id_ := (form_data_->>'hub_id')::bigint;
    pincodes_ := array(select jsonb_array_elements_text(form_data_->'pincodes'));
    
    -- Get user context for updates
    user_context_json = tms_get_user_context_from_data(form_data_);
    
    -- Get hub title
    select hub_name
      from public.cl_tx_vertical_srvc_hubs
     where id = hub_id_
       and org_id = org_id_
       and is_active is true
      into hub_title_;
    
    if hub_title_ is null then
        status = false;
        message = 'hub_not_found_or_inactive';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Get all service requests that fall under the vertical
    -- and have no hub assigned and have the pincode inside that hub
    select array_agg(db_id)
      from cl_tx_srvc_req
     where srvc_prvdr = org_id_
       and prvdr_vertical = vertical_id_
       and (prvdr_srvc_hub is null or prvdr_srvc_hub = 0)
       and form_data->>'cust_pincode' = any(pincodes_)
       and form_data->>'cust_pincode' is not null
       and form_data->>'cust_pincode' != ''
       and is_deleted is not true
      into srvc_req_ids;

    if srvc_req_ids is null or array_length(srvc_req_ids, 1) = 0 then
        status = true;
        message = 'no_service_requests_found';
        resp_data = json_build_object('updated_count', 0);
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Prepare the update JSON
    hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(hub_id_),true);
    hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(hub_title_),true);

    -- Update all service requests
    FOR i IN 1..array_length(srvc_req_ids, 1) LOOP
        perform tms_create_service_request(hub_update_json, srvc_req_ids[i]);
    END LOOP;

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'updated_count', array_length(srvc_req_ids, 1),
        'srvc_req_ids', srvc_req_ids,
        'hub_id', hub_id_,
        'hub_title', hub_title_
    );
    
    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
