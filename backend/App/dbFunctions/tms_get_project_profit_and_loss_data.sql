CREATE OR REPLACE FUNCTION public.tms_get_project_profit_and_loss_data(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
--Declaration
declare 
	status boolean;
	message text;
    resp_data json;
	srvc_req_id_ int;
	org_id_ int;
	profit_and_loss_data_ json;
	technician_data_fr_cost_breakdown_ json;
	user_custom_fields_ json;
begin
	resp_data = '{}'::json;
	status = false;
	message = 'internal_error';

	org_id_			   =  form_data_->>'org_id';
	srvc_req_id_      = form_data_->>'srvc_req_id';



	technician_data_fr_cost_breakdown_ = tms_get_inhouse_cost_breakdown(form_data_,srvc_req_id_);

	if technician_data_fr_cost_breakdown_ is not null then
		resp_data = jsonb_set(resp_data::jsonb,'{technician_data_fr_cost_breakdown}',to_jsonb(technician_data_fr_cost_breakdown_),true);
			
		status = true;
		message = 'success';
	end if;

	



	return jsonb_build_object('status',status,'code',message,'data',resp_data);

end;
$function$
;
