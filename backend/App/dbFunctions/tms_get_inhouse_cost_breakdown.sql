CREATE OR REPLACE FUNCTION public.tms_get_inhouse_cost_breakdown(form_data_ json, srvc_req_id_ integer)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
	inhouse_identity_key json;
	vertical_id_ int;
	org_id_ integer;
    field_value text;
    manday_cost int default 0;
begin
	org_id_ =  form_data_->>'org_id';
	vertical_id_ = form_data_->>'vertical_id';

	inhouse_identity_key =  (select settings_data
								      from cl_tx_orgs_settings
								     where db_id = vertical_id_
								       and settings_type = 'SP_CUSTOM_FIELDS');
								      
    field_value := inhouse_identity_key->>'project_profit_loss_manday_cost';

	IF field_value IS NOT NULL AND field_value <> '' THEN
	    manday_cost := field_value::int;
	ELSE
	    manday_cost := 0; -- or some default value
	    
	END IF;
     -- Check if required keys exist
	
	return (array_to_json(array(select jsonb_build_object(
			        'user_id', users.usr_id  ,
			        'mandays', count(closed_sbsk.db_id),
			        'mandays_cost', manday_cost::int	,			                      			                       
			        'expected_cost',count(closed_sbsk.db_id) * manday_cost::int,
			        'actual_cost', count(closed_sbsk.db_id) * manday_cost::int
					)
			  from cl_tx_sbtsk  as sbtsk
			 inner join cl_tx_sbtsk as closed_sbsk
			    on closed_sbsk.db_id = sbtsk.db_id
			 inner join cl_tx_users as users
			    on users.usr_id = any(sbtsk.assigned_to)
			   and users.form_data->>(inhouse_identity_key->>'project_profit_loss_user_inhouse_identity') = inhouse_identity_key->>'project_profit_loss_user_inhouse_identity_select_value'  
			 where sbtsk.srvc_req_id = srvc_req_id_
			   and sbtsk.org_id = org_id_
			   and sbtsk.is_deleted is not true
			 group by users.usr_id)) );						     
	
END;
$function$
;
