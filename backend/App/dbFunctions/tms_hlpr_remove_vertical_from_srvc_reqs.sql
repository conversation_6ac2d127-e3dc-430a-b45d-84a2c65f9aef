CREATE OR REPLACE FUNCTION public.tms_hlpr_remove_vertical_from_srvc_reqs(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    vertical_id_ integer;
    srvc_type_ids_to_exclude_ integer[];

    -- For processing
    user_context_json json;
    vertical_update_json json;
    srvc_req_record record;
    total_count integer;

begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';

    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    vertical_id_ := (form_data_->>'vertical_id')::integer;

    -- Convert JSON array to PostgreSQL array
    SELECT array_agg(value::integer)
      FROM json_array_elements_text(form_data_->'srvc_type_ids_to_exclude')
      INTO srvc_type_ids_to_exclude_;

    user_context_json = tms_get_user_context_from_data(form_data_);

    -- Get all service requests with this vertical_id that don't belong to the excluded service types
    -- Also retrieve the service type ID for each service request to avoid additional queries later
    CREATE TEMP TABLE temp_srvc_reqs AS
    SELECT db_id, srvc_type_id
      FROM cl_tx_srvc_req
     WHERE prvdr_vertical = vertical_id_
       AND srvc_type_id != ALL(srvc_type_ids_to_exclude_);

    -- Get row count of temp_srvc_reqs
    SELECT COUNT(*) INTO total_count FROM temp_srvc_reqs;

    if total_count = 0 then
        status = true;
        message = 'no_service_requests_found';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Prepare the update JSON to remove vertical_id
    vertical_update_json = jsonb_set(user_context_json::jsonb,'{vertical_id}',to_jsonb(0::integer),true);
    vertical_update_json = jsonb_set(vertical_update_json::jsonb,'{vertical_title}',to_jsonb(''::text),true);

    -- Update all service requests to remove vertical_id
    FOR srvc_req_record IN SELECT * FROM temp_srvc_reqs LOOP
        -- Set the service type ID for this service request
        vertical_update_json = jsonb_set(vertical_update_json::jsonb, '{srvc_type_id}', to_jsonb(srvc_req_record.srvc_type_id), true);

        -- Update the service request
        PERFORM tms_create_service_request(vertical_update_json, srvc_req_record.db_id::int);
    END LOOP;

    -- Drop the temporary table
    DROP TABLE IF EXISTS temp_srvc_reqs;

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'updated_count', total_count
    );

    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
