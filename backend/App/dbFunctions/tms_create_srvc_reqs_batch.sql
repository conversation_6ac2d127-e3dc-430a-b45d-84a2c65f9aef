CREATE OR REPLACE FUNCTION public.tms_create_srvc_reqs_batch(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	-- Bare minimums
	status boolean;
	message text;
	affected_rows integer;
	validation_resp text[];
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	srvc_type_id_ integer;
	srvc_pvdr_id_ integer;
	srvc_config_data_ json;
	-- Form data
	batch_data_ json;
	
	-- temp
  	_single_entry json;
  	ins_id bigint;
  	_form_data_proto json;
  	_single_entry_creation_resp json;
  	exception_hint text;
    _form_data_proto_json json;
  	_temp_single_order_data jsonb;
  	_temp_form_proto_fr_get_order_details jsonb;
  	_temp_is_api_call int;
  	_temp_existing_req_id_fr_api_call bigint;
  	_temp_other_unique_id_api text;
  	_for_deletion bool;
    _entry_ids_vs_query json default '{}';
  	_temp_existing_srvc_pvdr_id_fr_api_call bigint;
  	row_count_ int;
	srvc_prvdr_valitation_resp_ json;
	pincode_length_validation_resp_ json;
	_cust_pincode_ text;
	_sbtsk_entry_ids_vs_query_fr_deletion jsonb default '{}';
	_temp_is_bulk_update int;
	_temp_is_bulk_line_items_update int;
	_temp_existing_req_id_fr_bulk_update bigint;

	-- Output
	ins_ids bigint[];
    cust_org_id int;
   	api_call_resp json default '[]';
   	existing_req_ids_fr_api_call bigint[];
    validation_resp_ json;
	org_level_settings_data json;
	_config_data json;

begin
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(form_data_,'org_id');
	usr_id_ = json_extract_path_text(form_data_,'usr_id');
	ip_address_ = json_extract_path_text(form_data_,'ip_address');
	user_agent_ = json_extract_path_text(form_data_,'user_agent');
	srvc_type_id_ = json_extract_path_text(form_data_,'srvc_type_id');
	_for_deletion = form_data_->>'for_deletion';
--	raise notice 'Service id %',srvc_type_id_;
	-- Form data
	batch_data_ = form_data_->'batch_data';
	_form_data_proto = form_data_::jsonb - 'batch_data';
	_temp_is_api_call = (form_data_->>'is_api_call')::int;
	_temp_is_bulk_update = (form_data_->>'is_bulk_update')::int;
	_temp_is_bulk_line_items_update = (form_data_->>'is_bulk_line_items_update')::int;
	row_count_ = 0;

	--get config_data by srvc_type_id
    select srvc_type.form_data 
  	  from cl_cf_service_types as srvc_type 
     where service_type_id = srvc_type_id_ 
  	  into _config_data;
  	 
	--Get org_level_settings_data by org_id
	org_level_settings_data = tms_hlpr_get_org_level_settings_config_data_for_org(org_id_)->'data';

	for _single_entry in select * from json_array_elements(batch_data_)
	loop
		--raise notice '_single_entry%',_single_entry.cust_mobile;
	 	_form_data_proto_json = '{}'::jsonb;
		_form_data_proto_json = _form_data_proto::jsonb || _single_entry::jsonb;
--		raise notice 'Inserting entry for %',_single_entry#>>'{cust_full_name}';
--	    if length(_single_entry#>>'{cust_mobile}') != tms_hlpr_get_consumer_mobile_length_frm_org_lvl_settings(org_id_) then
--	    	message = 'Invalid phone number ' || (_single_entry->>'cust_mobile'); 
--	    	return json_build_object('status',false,'code',message);
--	    	--raise exception 'Failed: Invalid length for customer mobile %', _single_entry#>>'{cust_mobile}';
--	    end if;
	   
		if _temp_is_api_call > 0 then
			row_count_ = row_count_ + 1;

			/*Here, we're conducting basic validation for service requests to avoid UI crashes. 
			  If we need to add more validations for service requests in the future, we'll integrate them into this helper function, 
			  like custom field validations.
			*/
			validation_resp_ = tms_hlpr_srvc_req_validation(_config_data, org_level_settings_data, _form_data_proto_json);
			if (validation_resp_->>'status')::bool is not true then
				return json_build_object('status',false,'code',validation_resp_->>'message' || ' for row ' || (row_count_)::text);
			end if;

--			Check if the order already exists by checking this 79a88c7b-c64f-46c4-a277-bc80efa1c154 key in form_data
			_temp_existing_req_id_fr_api_call := 0;
			_temp_other_unique_id_api = _single_entry->>'79a88c7b-c64f-46c4-a277-bc80efa1c154';
			srvc_pvdr_id_ = _single_entry->>'service_provider_id';
			_cust_pincode_ = _single_entry->>'cust_pincode';
			if _temp_other_unique_id_api is null then
				_temp_other_unique_id_api = _single_entry->>'ext_order_id'; -- For batch deletion 
			end if;
		
			select srvc_req.db_id 	
	          from cl_tx_srvc_req as srvc_req 
	         where srvc_req.org_id = org_id_
	           and srvc_req.collab_order_id = _temp_other_unique_id_api
	         group by srvc_req.db_id
	         limit 1
	          into _temp_existing_req_id_fr_api_call;
	         
	       	if _temp_existing_req_id_fr_api_call is null then 
	       		select srvc_req.db_id 	
		          from cl_tx_srvc_req as srvc_req 
		         where srvc_req.org_id = org_id_
		           and srvc_req.srvc_type_id = srvc_type_id_
		           and srvc_req.display_code = _single_entry->>'tms_display_code'
		         group by srvc_req.db_id
		         limit 1
		          into _temp_existing_req_id_fr_api_call;
	       	end if;
	       
	       if srvc_pvdr_id_ is not null then
				srvc_prvdr_valitation_resp_ = tms_hlpr_validate_srvc_prvdr_fr_srvc_type(srvc_type_id_,srvc_pvdr_id_);
	       		if (srvc_prvdr_valitation_resp_->>'status')::bool is not true then
	       			if srvc_prvdr_valitation_resp_->>'codeKey' = 'cannot_assign_service_provider' then         	         				   		
				   		return json_build_object('status',status,'code','cannot assign service provider for '|| (row_count_)::text,'data','service_provider_id');
				    end if;

					if srvc_prvdr_valitation_resp_->>'codeKey' = 'invalid_service_provider_id' then
						return json_build_object('status',status,'code','Invalid service provider id for '|| (row_count_)::text,'data','service_provider_id');
					end if;
	       		end if;		
	       		_temp_existing_srvc_pvdr_id_fr_api_call = srvc_type_id_;
		   end if;
		  
		  	
		  	
	       
	       	--raise notice '_temp_existing_req_id_fr_api_call %',_temp_existing_req_id_fr_api_call;
	        if _temp_existing_req_id_fr_api_call > 0 then
	        	existing_req_ids_fr_api_call := existing_req_ids_fr_api_call || _temp_existing_req_id_fr_api_call;
	        	if _for_deletion is not null then
	        		_form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb,'{update_deletion}'::text[],to_jsonb(true),true);
	        	end if;
	        	if _temp_existing_srvc_pvdr_id_fr_api_call is not null then
	        		_form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb,'{new_prvdr}'::text[],to_jsonb(srvc_pvdr_id_),true);
	        	end if;
	        	_single_entry_creation_resp = tms_create_service_request(_form_data_proto_json,_temp_existing_req_id_fr_api_call::int);
	        else 
	        	_single_entry_creation_resp = tms_create_service_request(_form_data_proto_json);
	        end if;
	       
	    elseif _temp_is_bulk_update > 0 or _temp_is_bulk_line_items_update > 0 then

			select srvc_req.db_id 	
			  from cl_tx_srvc_req as srvc_req 
			 where srvc_req.org_id = org_id_
			   and srvc_req.srvc_type_id = srvc_type_id_
			   and srvc_req.display_code = _single_entry->>'tms_display_code'
			 group by srvc_req.db_id
			 limit 1
			  into _temp_existing_req_id_fr_bulk_update;

				-- raise notice '_temp_existing_req_id_fr_bulk_update %',_temp_existing_req_id_fr_bulk_update;

			if _temp_existing_req_id_fr_bulk_update > 0 then
				_single_entry_creation_resp = tms_create_service_request(_form_data_proto_json,_temp_existing_req_id_fr_bulk_update::int);
			else
				exception_hint = 'Failed row --> Invalid request id: ' || (_single_entry#>>'{tms_display_code}')::text;
				raise exception 'Failed row --> %', _single_entry#>>'{tms_display_code}'
					using HINT = exception_hint;
			end if;	
		else
	    	_single_entry_creation_resp = tms_create_service_request(_form_data_proto_json);
		end if;
		--raise notice '_single_entry_creation_resp %',_single_entry_creation_resp;
--	    raise notice '_form_data_proto %', _form_data_proto_json;
		exception_hint = 'Failed row --> ' || 
     				(_single_entry#>>'{cust_full_name}') || 
     				' ' || 
     				(_single_entry_creation_resp#>>'{code}');
     	if exception_hint is null then
     		exception_hint = 'Failed row --> ' || (_single_entry)::text;
     	end if;
     	perform pg_sleep(0.000001);-- wait 1 microsecond 		
--		raise exception 'Failed row --> %', _single_entry#>>'{cust_full_name}'
--     			 using HINT = exception_hint;
	   
		if _single_entry_creation_resp->'status' then
--			raise notice 'entry_id %',(_single_entry_creation_resp->'data'#>>'{entry_id}');
			-- entry creation successful
			ins_ids := ins_ids || (_single_entry_creation_resp->'data'#>>'{entry_id}')::bigint;
			if _temp_is_api_call > 0 then
				_temp_form_proto_fr_get_order_details = jsonb_build_object(
					'display_titles', jsonb_build_array(
						_single_entry_creation_resp->'data'->'display_code'
					)
				);
				
				_temp_form_proto_fr_get_order_details = _temp_form_proto_fr_get_order_details || _form_data_proto::jsonb;
				
				_temp_single_order_data = jsonb_build_object(
					'input_data',_single_entry
				);
				_temp_single_order_data = _temp_single_order_data || (
					tms_api_brand_get_order_details(_temp_form_proto_fr_get_order_details::json)->'data'->0
				)::jsonb;
				
			
				api_call_resp := api_call_resp::jsonb || (json_build_array(_temp_single_order_data))::jsonb ;
			end if;
			
		
		    cust_org_id = (_single_entry_creation_resp->'data'#>>'{cust_org_id}')::int; 
		    _entry_ids_vs_query := _entry_ids_vs_query::jsonb || json_build_object((_single_entry_creation_resp->'data'#>>'{entry_id}'), _form_data_proto_json)::jsonb;
		    
		    if _single_entry_creation_resp->'data'->'entry_id_vs_query_fr_deletion' is not null then
		   	_sbtsk_entry_ids_vs_query_fr_deletion := _sbtsk_entry_ids_vs_query_fr_deletion || to_jsonb(_single_entry_creation_resp->'data'->'entry_id_vs_query_fr_deletion');
		    end if;
		else
			raise exception 'Failed row --> %', _single_entry#>>'{cust_full_name}'
     			 using HINT = exception_hint;
		end if;
	
	end loop;
	
	status = true;
	message = 'success';
	resp_data =  json_build_object('entry_ids',ins_ids,'cust_org_id',cust_org_id,'api_call_resp',api_call_resp,
'existing_req_ids_fr_api_call',existing_req_ids_fr_api_call,'entry_ids_vs_query',_entry_ids_vs_query,'entry_id_vs_query_fr_deletion',_sbtsk_entry_ids_vs_query_fr_deletion);

	return json_build_object('status',status,'code',message,'data',resp_data);
 
END;
$function$
;
