create or replace function public.tms_ace_hlpr_update_srvc_req_hub(srvc_req_id integer, form_data_ json)
 returns json
 language plpgsql
as $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    srvc_type_id_ integer;

    provider_id_ integer;
    vertical_id_ integer;
    pincode_ text;

    hub_id_ bigint;
    hub_title_ text;

    user_context_json json;
    srvc_hub_update_json json;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    srvc_type_id_ := (form_data_->>'srvc_type_id')::integer;

    user_context_json = tms_get_user_context_from_data(form_data_);
    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(form_data_->>'srvc_type_id'),true);      


    select srvc_req.form_data->>'cust_pincode',
           srvc_req.prvdr_vertical,
           srvc_req.srvc_prvdr 
      from cl_tx_srvc_req as srvc_req
     where srvc_req.db_id = srvc_req_id
       and srvc_type_id = srvc_type_id_
      into pincode_, vertical_id_, provider_id_;

    if pincode_ is null or vertical_id_ is null or provider_id_ is null then
        -- clear the existing service hub mapping
        srvc_hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(0::bigint),true);
        srvc_hub_update_json = jsonb_set(srvc_hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(''::text),true);
        perform tms_create_service_request(srvc_hub_update_json,srvc_req_id);
        
        raise notice 'srvc_req_id % incomplete for hub update', srvc_req_id;
        status = true;
        message = 'incomplete_for_srvc_hub_update';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Get matching service hub
    select hub.id, hub.hub_name
      from public.cl_tx_vertical_srvc_hubs as hub
     where hub.org_id = provider_id_
       and hub.vertical_id = vertical_id_
       and hub.is_active is true
       and pincode_ = any(hub.pincodes)
      into hub_id_, hub_title_;

    if hub_id_ > 0 then
        srvc_hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(hub_id_),true);
        srvc_hub_update_json = jsonb_set(srvc_hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(hub_title_),true);
        perform tms_create_service_request(srvc_hub_update_json,srvc_req_id);

        status = true;
        message = 'success';
        resp_data = '{}';
    else
        -- clear the existing service hub mapping
        srvc_hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(0::bigint),true);
        srvc_hub_update_json = jsonb_set(srvc_hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(''::text),true);
        perform tms_create_service_request(srvc_hub_update_json,srvc_req_id);

        status = true;
        message = 'no_srvc_hub_found';
        resp_data = '{}';
    end if;   
    
    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
